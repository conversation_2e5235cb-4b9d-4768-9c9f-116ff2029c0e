/**
 * AI Onboarding Routes
 * API endpoints for AI-powered onboarding system
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { aiConversationService } from '../services/aiConversation';
import { openRouterService } from '../services/openrouter';
import { enhancedAIService } from '../services/ai/enhancedAIService';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';
import { asyncHandler } from '../utils/asyncHandler';
import { supabase } from '../config/supabase';

const router = Router();

/**
 * @swagger
 * /api/ai/conversation/start:
 *   post:
 *     summary: Start new AI conversation session
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userRole
 *               - language
 *             properties:
 *               userRole:
 *                 type: string
 *                 enum: [CLIENT, EXPERT]
 *               language:
 *                 type: string
 *                 enum: [ar, en]
 *               sessionType:
 *                 type: string
 *                 enum: [onboarding, profile_optimization, service_creation]
 *                 default: onboarding
 *     responses:
 *       201:
 *         description: Conversation session started successfully
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Authentication required
 */
router.post(
  '/conversation/start',
  authenticate,
  [
    body('userRole')
      .isIn(['CLIENT', 'EXPERT'])
      .withMessage('User role must be CLIENT or EXPERT'),
    body('language')
      .isIn(['ar', 'en'])
      .withMessage('Language must be ar or en'),
    body('sessionType')
      .optional()
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { userRole, language, sessionType = 'onboarding' } = req.body;
    const userId = req.user!.id;

    const session = await aiConversationService.startConversation({
      userId,
      userRole,
      language,
      sessionType,
    });

    logger.info('AI conversation started via API', {
      userId,
      sessionId: session.id,
      userRole,
      language,
      sessionType,
    });

    res.status(201).json({
      success: true,
      message: 'Conversation session started successfully',
      data: {
        sessionId: session.id,
        currentStep: session.currentStep,
        messages: session.messages,
        extractedData: session.extractedData,
        status: session.status,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversation/{sessionId}/message:
 *   post:
 *     summary: Send message to AI conversation
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *     responses:
 *       200:
 *         description: Message processed successfully
 *       400:
 *         description: Invalid message or session
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Session not found
 */
router.post(
  '/conversation/:sessionId/message',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { message } = req.body;
    const userId = req.user!.id;

    // Verify session ownership
    const session = aiConversationService.getSession(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.userId !== userId) {
      throw createError.forbidden('Access denied to this conversation session');
    }

    const result = await aiConversationService.processMessage(sessionId, message);

    logger.info('AI message processed via API', {
      userId,
      sessionId,
      messageLength: message.length,
      currentStep: result.session.currentStep,
    });

    res.json({
      success: true,
      message: 'Message processed successfully',
      data: {
        sessionId,
        userMessage: {
          content: message,
          timestamp: new Date(),
        },
        aiResponse: {
          content: result.aiResponse.content,
          timestamp: result.aiResponse.timestamp,
        },
        currentStep: result.session.currentStep,
        extractedData: result.session.extractedData,
        isCompleted: result.session.status === 'completed',
        recommendations: result.session.recommendations,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversation/{sessionId}:
 *   get:
 *     summary: Get conversation session details
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session details retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Session not found
 */
router.get(
  '/conversation/:sessionId',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user!.id;

    const session = aiConversationService.getSession(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.userId !== userId) {
      throw createError.forbidden('Access denied to this conversation session');
    }

    res.json({
      success: true,
      message: 'Session details retrieved successfully',
      data: {
        sessionId: session.id,
        sessionType: session.sessionType,
        userRole: session.userRole,
        language: session.language,
        currentStep: session.currentStep,
        status: session.status,
        messages: session.messages,
        extractedData: session.extractedData,
        recommendations: session.recommendations,
        createdAt: session.createdAt,
        lastActiveAt: session.lastActiveAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversations:
 *   get:
 *     summary: Get user's conversation sessions
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, abandoned, paused]
 *       - in: query
 *         name: sessionType
 *         schema:
 *           type: string
 *           enum: [onboarding, profile_optimization, service_creation]
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: User sessions retrieved successfully
 *       401:
 *         description: Authentication required
 */
router.get(
  '/conversations',
  authenticate,
  [
    query('status')
      .optional()
      .isIn(['active', 'completed', 'abandoned', 'paused'])
      .withMessage('Invalid status filter'),
    query('sessionType')
      .optional()
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type filter'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { status, sessionType, limit = 20 } = req.query;

    let sessions = aiConversationService.getUserSessions(userId);

    // Apply filters
    if (status) {
      sessions = sessions.filter(session => session.status === status);
    }

    if (sessionType) {
      sessions = sessions.filter(session => session.sessionType === sessionType);
    }

    // Apply limit
    sessions = sessions.slice(0, Number(limit));

    res.json({
      success: true,
      message: 'User sessions retrieved successfully',
      data: {
        sessions: sessions.map(session => ({
          sessionId: session.id,
          sessionType: session.sessionType,
          userRole: session.userRole,
          language: session.language,
          currentStep: session.currentStep,
          status: session.status,
          messageCount: session.messages.length,
          createdAt: session.createdAt,
          lastActiveAt: session.lastActiveAt,
        })),
        total: sessions.length,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/test-connection:
 *   get:
 *     summary: Test OpenRouter API connection
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Connection test successful
 *       500:
 *         description: Connection test failed
 */
router.get(
  '/test-connection',
  authenticate,
  asyncHandler(async (req, res) => {
    const isConnected = await openRouterService.testConnection();
    const availableModels = await openRouterService.getAvailableModels();

    res.json({
      success: true,
      message: 'OpenRouter API connection test completed',
      data: {
        connected: isConnected,
        availableModels: availableModels.length,
        models: availableModels.slice(0, 10), // Return first 10 models
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/conversation/start:
 *   post:
 *     summary: Start enhanced AI conversation with advanced features
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userRole
 *               - sessionType
 *             properties:
 *               userRole:
 *                 type: string
 *                 enum: [CLIENT, EXPERT]
 *               sessionType:
 *                 type: string
 *                 enum: [onboarding, profile_optimization, service_creation]
 *     responses:
 *       201:
 *         description: Enhanced conversation session started successfully
 */
router.post(
  '/enhanced/conversation/start',
  authenticate,
  [
    body('userRole')
      .isIn(['CLIENT', 'EXPERT'])
      .withMessage('User role must be CLIENT or EXPERT'),
    body('sessionType')
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { userRole, sessionType } = req.body;
    const userId = req.user!.id;

    const result = await enhancedAIService.startEnhancedConversation(
      userId,
      sessionType,
      userRole
    );

    logger.info('Enhanced AI conversation started', {
      userId,
      sessionId: result.sessionId,
      userRole,
      sessionType,
    });

    res.status(201).json({
      success: true,
      message: 'Enhanced conversation session started successfully',
      data: {
        sessionId: result.sessionId,
        welcomeMessage: result.welcomeMessage,
        currentStep: result.currentStep,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/conversation/{sessionId}/message:
 *   post:
 *     summary: Send message to enhanced AI conversation with confidence scoring
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *               - currentStep
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *               currentStep:
 *                 type: string
 *     responses:
 *       200:
 *         description: Message processed with AI analysis
 */
router.post(
  '/enhanced/conversation/:sessionId/message',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
    body('currentStep')
      .isString()
      .notEmpty()
      .withMessage('Current step is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { message, currentStep } = req.body;
    const userId = req.user!.id;

    const result = await enhancedAIService.processUserMessage(
      sessionId,
      message,
      currentStep
    );

    logger.info('Enhanced AI message processed', {
      userId,
      sessionId,
      currentStep,
      confidence: result.confidence,
      nextStep: result.nextStep,
    });

    res.json({
      success: true,
      message: 'Message processed with AI analysis',
      data: {
        aiResponse: result.aiResponse,
        nextStep: result.nextStep,
        extractedData: result.extractedData,
        confidence: result.confidence,
        recommendations: result.recommendations,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/profile/generate/{sessionId}:
 *   post:
 *     summary: Generate expert profile from AI conversation data
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Expert profile generated successfully
 */
router.post(
  '/enhanced/profile/generate/:sessionId',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user!.id;

    const generatedProfile = await enhancedAIService.generateExpertProfile(sessionId);

    logger.info('Expert profile generated from AI data', {
      userId,
      sessionId,
      profileGenerated: true,
    });

    res.json({
      success: true,
      message: 'Expert profile generated successfully',
      data: {
        profile: generatedProfile,
        sessionId,
      },
    });
  })
);

/**
 * Phase 3: Advanced AI Features Integration
 * Real-time conversation management with Syrian cultural context
 */

/**
 * @swagger
 * /api/ai/v2/conversation/start:
 *   post:
 *     summary: Start Phase 3 AI conversation with real-time capabilities
 *     tags: [Phase 3 AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userRole
 *               - language
 *             properties:
 *               userRole:
 *                 type: string
 *                 enum: [CLIENT, EXPERT]
 *               language:
 *                 type: string
 *                 enum: [ar, en]
 *                 default: ar
 *               sessionType:
 *                 type: string
 *                 enum: [onboarding, profile_optimization, service_creation, skill_assessment]
 *                 default: onboarding
 *               culturalContext:
 *                 type: object
 *                 properties:
 *                   location:
 *                     type: string
 *                     description: Syrian city/region
 *                   dialect:
 *                     type: string
 *                     enum: [damascus, aleppo, homs, latakia, general]
 *                     default: general
 *     responses:
 *       201:
 *         description: Phase 3 AI conversation started successfully
 */
router.post(
  '/v2/conversation/start',
  authenticate,
  [
    body('userRole')
      .isIn(['CLIENT', 'EXPERT'])
      .withMessage('User role must be CLIENT or EXPERT'),
    body('language')
      .isIn(['ar', 'en'])
      .withMessage('Language must be ar or en'),
    body('sessionType')
      .optional()
      .isIn(['onboarding', 'profile_optimization', 'service_creation', 'skill_assessment'])
      .withMessage('Invalid session type'),
    body('culturalContext.location')
      .optional()
      .isString()
      .withMessage('Location must be a string'),
    body('culturalContext.dialect')
      .optional()
      .isIn(['damascus', 'aleppo', 'homs', 'latakia', 'general'])
      .withMessage('Invalid Syrian dialect'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    // For development/testing without Supabase, return a mock response
    const {
      userRole,
      language = 'ar',
      sessionType = 'onboarding',
      culturalContext = { location: '', dialect: 'general' }
    } = req.body;
    const userId = req.user!.id;

    // Generate mock session ID
    const sessionId = `mock_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Generate mock welcome message
    const welcomeMessage = userRole === 'EXPERT'
      ? 'مرحباً بك! أنا مساعدك الذكي في فريلا سوريا. سأساعدك في إعداد ملفك الشخصي كخبير. دعنا نبدأ بالتعرف على مهاراتك وخبراتك.'
      : 'مرحباً بك! أنا مساعدك الذكي في فريلا سوريا. سأساعدك في العثور على الخبراء المناسبين لمشاريعك. دعنا نبدأ بفهم احتياجاتك.';

    logger.info('Mock AI conversation started', {
      userId,
      sessionId,
      userRole,
      language,
      sessionType,
    });

    res.status(201).json({
      success: true,
      message: 'AI conversation started successfully',
      data: {
        sessionId,
        welcomeMessage,
        currentStep: 'welcome',
        culturalContext: {
          country: 'Syria',
          language: language === 'ar' ? 'Arabic' : 'English',
          dialect: culturalContext.dialect || 'general',
          location: culturalContext.location || '',
        },
        features: {
          realTimeProcessing: true,
          skillExtraction: true,
          marketIntelligence: true,
          culturalAdaptation: true,
          voiceSupport: true,
          imageAnalysis: true
        },
        nextSteps: sessionType === 'onboarding'
          ? ['personal_info', 'skills_assessment', 'experience_review', 'portfolio_analysis', 'pricing_strategy', 'market_positioning', 'profile_optimization', 'completion']
          : ['current_analysis', 'improvement_suggestions', 'implementation_plan', 'completion']
      },
    });

    // Create enhanced conversation context with Syrian cultural awareness
    const conversationContext = {
      userId,
      userRole,
      language,
      sessionType,
      culturalContext: {
        country: 'Syria',
        language: language === 'ar' ? 'Arabic' : 'English',
        dialect: culturalContext.dialect || 'general',
        location: culturalContext.location || '',
        marketContext: 'Syrian freelance market',
        economicContext: 'Developing economy with focus on digital services',
        culturalNotes: [
          'Respectful and professional communication',
          'Family-oriented work-life balance considerations',
          'Local market pricing awareness',
          'Regional skill demands understanding'
        ]
      },
      aiModel: 'openai/gpt-4-turbo-preview',
      features: {
        realTimeProcessing: true,
        skillExtraction: true,
        marketIntelligence: true,
        culturalAdaptation: true,
        voiceSupport: true,
        imageAnalysis: true
      }
    };

    // Generate culturally-aware welcome message
    const welcomePrompt = `
أنت مساعد ذكي متخصص في منصة فريلا سوريا للعمل الحر. مهمتك مساعدة ${userRole === 'EXPERT' ? 'الخبراء' : 'العملاء'} السوريين في ${sessionType === 'onboarding' ? 'إعداد ملفاتهم الشخصية' : 'تحسين خدماتهم'}.

السياق الثقافي:
- المستخدم من سوريا، ${culturalContext.location ? `من منطقة ${culturalContext.location}` : 'من مختلف المناطق السورية'}
- اللهجة المفضلة: ${culturalContext.dialect === 'general' ? 'عربية فصحى مع تعابير محلية' : `لهجة ${culturalContext.dialect}`}
- السوق المستهدف: السوق السوري والعربي للخدمات الرقمية

اكتب رسالة ترحيب دافئة ومهنية تعكس الثقافة السورية وتشرح كيف ستساعد المستخدم في رحلته على المنصة. استخدم اللغة العربية مع مراعاة السياق الثقافي السوري.
`;

    try {
      const welcomeResponse = await openRouterService.chatCompletion(
        [{ role: 'user', content: welcomePrompt }],
        {},
        {
          temperature: 0.8,
          maxTokens: 300,
        }
      );

      // Create session with Supabase integration
      const sessionData = {
        user_id: userId,
        session_type: sessionType,
        user_role: userRole,
        language,
        cultural_context: conversationContext.culturalContext,
        status: 'active',
        current_step: 'welcome',
        total_steps: sessionType === 'onboarding' ? 8 : 5,
        extracted_data: {},
        ai_model: conversationContext.aiModel,
        features_enabled: conversationContext.features
      };

      const { data: session, error } = await supabase
        .from('ai_chat_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        logger.error('Failed to create AI session in Supabase', { error, userId });
        throw createError.internalServerError('Failed to create conversation session');
      }

      // Store welcome message
      await supabase
        .from('ai_chat_messages')
        .insert({
          session_id: session.id,
          role: 'assistant',
          content: welcomeResponse.content,
          message_type: 'welcome',
          confidence_score: 0.95,
          processing_time: 0
        });

      logger.info('Phase 3 AI conversation started', {
        userId,
        sessionId: session.id,
        userRole,
        language,
        sessionType,
        culturalContext: conversationContext.culturalContext
      });

      res.status(201).json({
        success: true,
        message: 'Phase 3 AI conversation started successfully',
        data: {
          sessionId: session.id,
          welcomeMessage: welcomeResponse.content,
          currentStep: 'welcome',
          culturalContext: conversationContext.culturalContext,
          features: conversationContext.features,
          nextSteps: sessionType === 'onboarding'
            ? ['personal_info', 'skills_assessment', 'experience_review', 'portfolio_analysis', 'pricing_strategy', 'market_positioning', 'profile_optimization', 'completion']
            : ['current_analysis', 'improvement_suggestions', 'implementation_plan', 'completion']
        },
      });

    } catch (error) {
      logger.error('Failed to start Phase 3 AI conversation', { error, userId });
      throw createError.internalServerError('Failed to start AI conversation');
    }
  })
);

/**
 * @swagger
 * /api/ai/v2/conversation/{sessionId}/message:
 *   post:
 *     summary: Send message to Phase 3 AI conversation with advanced processing
 *     tags: [Phase 3 AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 3000
 *               messageType:
 *                 type: string
 *                 enum: [text, voice_transcript, image_description]
 *                 default: text
 *               metadata:
 *                 type: object
 *                 properties:
 *                   voiceData:
 *                     type: object
 *                   imageData:
 *                     type: object
 *     responses:
 *       200:
 *         description: Message processed with advanced AI analysis
 */
router.post(
  '/v2/conversation/:sessionId/message',
  authenticate,
  [
    param('sessionId')
      .isUUID()
      .withMessage('Session ID must be a valid UUID'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 3000 })
      .withMessage('Message must be between 1 and 3000 characters'),
    body('messageType')
      .optional()
      .isIn(['text', 'voice_transcript', 'image_description'])
      .withMessage('Invalid message type'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { message, messageType = 'text', metadata = {} } = req.body;
    const userId = req.user!.id;

    try {
      // Get session from Supabase
      const { data: session, error: sessionError } = await supabase
        .from('ai_chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single();

      if (sessionError || !session) {
        throw createError.notFound('Conversation session not found');
      }

      if (session.status === 'completed') {
        throw createError.badRequest('Conversation session is already completed');
      }

      // Store user message
      const { data: userMessage } = await supabase
        .from('ai_chat_messages')
        .insert({
          session_id: sessionId,
          role: 'user',
          content: message,
          message_type: messageType,
          metadata
        })
        .select()
        .single();

      // Get conversation history for context
      const { data: messageHistory } = await supabase
        .from('ai_chat_messages')
        .select('role, content, message_type, confidence_score')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })
        .limit(20);

      // Build conversation context with Syrian cultural awareness
      const conversationMessages = messageHistory?.map((msg: any) => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      })) || [];

      // Add current user message
      conversationMessages.push({
        role: 'user',
        content: message
      });

      // Create enhanced system prompt with Syrian context
      const systemPrompt = `أنت مساعد ذكي متخصص في منصة فريلا سوريا للعمل الحر. تساعد ${session.user_role === 'EXPERT' ? 'الخبراء' : 'العملاء'} السوريين في ${session.session_type}.

السياق الثقافي والجغرافي:
- البلد: سوريا
- اللغة: ${session.language === 'ar' ? 'العربية' : 'الإنجليزية'}
- السوق المستهدف: السوق السوري والعربي
- المرحلة الحالية: ${session.current_step}

مهامك الأساسية:
1. تحليل رسائل المستخدم واستخراج المعلومات المهمة
2. تقديم نصائح مخصصة للسوق السوري
3. مساعدة في تحديد الأسعار المناسبة للسوق المحلي
4. تقديم اقتراحات تتماشى مع الثقافة السورية
5. استخدام اللغة العربية الواضحة مع تعابير محلية مناسبة

إرشادات الاستجابة:
- كن مهنياً ومحترماً
- راعي الظروف الاقتصادية في سوريا
- اقترح حلول عملية وقابلة للتطبيق
- استخدم أمثلة من السوق السوري عند الإمكان
- كن مشجعاً ومتفائلاً

نوع الرسالة الحالية: ${messageType}
${metadata.voiceData ? 'تم تحويل الرسالة من صوت إلى نص' : ''}
${metadata.imageData ? 'تحتوي الرسالة على وصف صورة' : ''}`;

      // Add system message to conversation
      const messagesWithSystem = [
        { role: 'system' as const, content: systemPrompt },
        ...conversationMessages
      ];

      // Get AI response with enhanced processing
      const startTime = Date.now();
      const aiResponse = await openRouterService.chatCompletion(
        messagesWithSystem,
        {},
        {
          temperature: 0.7,
          maxTokens: 800,
          model: session.ai_model || 'openai/gpt-4-turbo-preview'
        }
      );
      const processingTime = Date.now() - startTime;

      // Extract data from conversation using AI
      const extractionPrompt = `
تحليل المحادثة التالية واستخراج المعلومات المهمة بصيغة JSON:

المحادثة:
المستخدم: ${message}
المساعد: ${aiResponse.content}

استخرج المعلومات التالية إذا كانت متوفرة:
- المهارات المذكورة
- سنوات الخبرة
- نوع المشاريع
- الأسعار المقترحة
- الموقع الجغرافي
- التفضيلات الشخصية
- التحديات المذكورة

أجب بصيغة JSON فقط:
{
  "skills": [],
  "experience_years": null,
  "project_types": [],
  "pricing_info": {},
  "location": "",
  "preferences": [],
  "challenges": [],
  "confidence_score": 0.0
}`;

      const extractionResponse = await openRouterService.chatCompletion(
        [{ role: 'user', content: extractionPrompt }],
        {},
        {
          temperature: 0.3,
          maxTokens: 500,
          model: 'openai/gpt-4-turbo-preview'
        }
      );

      let extractedData: any = {};
      let confidenceScore = 0.7;

      try {
        extractedData = JSON.parse(extractionResponse.content);
        confidenceScore = extractedData.confidence_score || 0.7;
      } catch (parseError) {
        logger.warn('Failed to parse extraction data', { parseError, sessionId });
      }

      // Store AI response
      const { data: aiMessage } = await supabase
        .from('ai_chat_messages')
        .insert({
          session_id: sessionId,
          role: 'assistant',
          content: aiResponse.content,
          message_type: 'response',
          confidence_score: confidenceScore,
          processing_time: processingTime,
          extracted_data: extractedData
        })
        .select()
        .single();

      // Update session with extracted data
      const updatedExtractedData = {
        ...session.extracted_data,
        ...extractedData,
        last_update: new Date().toISOString()
      };

      // Determine next step based on conversation progress
      const nextStep = await determineNextStep(session, extractedData);
      const completionRate = calculateCompletionRate(nextStep, session.total_steps);

      await supabase
        .from('ai_chat_sessions')
        .update({
          current_step: nextStep,
          extracted_data: updatedExtractedData,
          completion_rate: completionRate,
          last_activity: new Date().toISOString(),
          status: completionRate >= 100 ? 'completed' : 'active'
        })
        .eq('id', sessionId);

      logger.info('Phase 3 AI message processed', {
        userId,
        sessionId,
        messageType,
        processingTime,
        confidenceScore,
        nextStep,
        completionRate
      });

      res.json({
        success: true,
        message: 'Message processed successfully with advanced AI analysis',
        data: {
          sessionId,
          userMessage: {
            id: userMessage?.id,
            content: message,
            type: messageType,
            timestamp: userMessage?.created_at
          },
          aiResponse: {
            id: aiMessage?.id,
            content: aiResponse.content,
            timestamp: aiMessage?.created_at,
            confidenceScore,
            processingTime
          },
          extractedData,
          sessionProgress: {
            currentStep: nextStep,
            completionRate,
            isCompleted: completionRate >= 100,
            nextStepSuggestion: getNextStepSuggestion(nextStep, session.session_type)
          },
          culturalContext: session.cultural_context
        },
      });

    } catch (error) {
      logger.error('Failed to process Phase 3 AI message', { error, sessionId, userId });
      throw createError.internalServerError('Failed to process message');
    }
  })
);

// Helper functions
async function determineNextStep(session: any, extractedData: any): Promise<string> {
  const currentStep = session.current_step;
  const sessionType = session.session_type;

  if (sessionType === 'onboarding') {
    const steps = ['welcome', 'personal_info', 'skills_assessment', 'experience_review', 'portfolio_analysis', 'pricing_strategy', 'market_positioning', 'profile_optimization', 'completion'];
    const currentIndex = steps.indexOf(currentStep);

    // Check if current step has enough data to proceed
    const hasEnoughData = checkStepCompletion(currentStep, extractedData);

    if (hasEnoughData && currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
  }

  return currentStep;
}

function checkStepCompletion(step: string, extractedData: any): boolean {
  switch (step) {
    case 'welcome':
      return true; // Always proceed after welcome
    case 'personal_info':
      return extractedData.location || extractedData.preferences?.length > 0;
    case 'skills_assessment':
      return extractedData.skills?.length > 0;
    case 'experience_review':
      return extractedData.experience_years !== null || extractedData.project_types?.length > 0;
    default:
      return true;
  }
}

function calculateCompletionRate(currentStep: string, totalSteps: number): number {
  const steps = ['welcome', 'personal_info', 'skills_assessment', 'experience_review', 'portfolio_analysis', 'pricing_strategy', 'market_positioning', 'profile_optimization', 'completion'];
  const currentIndex = steps.indexOf(currentStep);
  return Math.round((currentIndex / (totalSteps - 1)) * 100);
}

function getNextStepSuggestion(currentStep: string, sessionType: string): string {
  const suggestions: Record<string, string> = {
    'welcome': 'أخبرني عن نفسك وخبراتك المهنية',
    'personal_info': 'ما هي مهاراتك الأساسية؟',
    'skills_assessment': 'حدثني عن مشاريعك السابقة',
    'experience_review': 'هل لديك أعمال سابقة يمكن عرضها؟',
    'portfolio_analysis': 'كيف تفكر في تسعير خدماتك؟',
    'pricing_strategy': 'كيف تريد أن تميز نفسك في السوق؟',
    'market_positioning': 'دعنا نحسن ملفك الشخصي',
    'profile_optimization': 'تهانينا! ملفك جاهز للنشر'
  };

  return suggestions[currentStep] || 'تابع المحادثة لإكمال إعداد ملفك';
}

/**
 * @swagger
 * /api/ai/v2/conversation/{sessionId}/voice:
 *   post:
 *     summary: Process voice message in AI conversation
 *     tags: [Phase 3 AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - audioFile
 *             properties:
 *               audioFile:
 *                 type: string
 *                 format: binary
 *               transcription:
 *                 type: string
 *                 description: Pre-transcribed text (optional)
 *               dialect:
 *                 type: string
 *                 enum: [damascus, aleppo, homs, latakia, general]
 *                 default: general
 *     responses:
 *       200:
 *         description: Voice message processed successfully
 */
router.post(
  '/v2/conversation/:sessionId/voice',
  authenticate,
  [
    param('sessionId')
      .isUUID()
      .withMessage('Session ID must be a valid UUID'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { transcription, dialect = 'general' } = req.body;
    const userId = req.user!.id;

    try {
      // Get session from Supabase
      const { data: session, error: sessionError } = await supabase
        .from('ai_chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single();

      if (sessionError || !session) {
        throw createError.notFound('Conversation session not found');
      }

      // For now, use the provided transcription
      // In production, you would integrate with a speech-to-text service
      let processedTranscription = transcription;

      if (!processedTranscription) {
        // Simulate voice processing
        processedTranscription = "تم تحويل الرسالة الصوتية إلى نص. يرجى تقديم النص المكتوب للمعالجة.";
      }

      // Process the transcribed message as a regular text message
      const messageData = {
        message: processedTranscription,
        messageType: 'voice_transcript',
        metadata: {
          voiceData: {
            dialect,
            originalAudio: true,
            transcriptionMethod: 'manual', // or 'automatic'
            confidence: 0.85
          }
        }
      };

      // Create enhanced system prompt for voice processing
      const voiceSystemPrompt = `أنت مساعد ذكي متخصص في معالجة الرسائل الصوتية للمستخدمين السوريين على منصة فريلا سوريا.

خصائص معالجة الصوت:
- اللهجة المحددة: ${dialect}
- تم تحويل الرسالة من صوت إلى نص
- قد تحتوي على أخطاء في التحويل
- راعي الطبيعة التفاعلية للمحادثة الصوتية

إرشادات الاستجابة:
- اشكر المستخدم على استخدام الرسائل الصوتية
- كن أكثر تفاعلية وودية في الرد
- اطرح أسئلة توضيحية إذا كان النص غير واضح
- استخدم تعابير مناسبة للمحادثة الصوتية`;

      const conversationMessages = [
        { role: 'system' as const, content: voiceSystemPrompt },
        { role: 'user' as const, content: processedTranscription }
      ];

      const aiResponse = await openRouterService.chatCompletion(
        conversationMessages,
        {
          userId: session.user_id,
          sessionId: session.id,
          userRole: session.user_role,
          language: session.language,
          currentStep: session.current_step,
          extractedData: session.extracted_data
        },
        {
          temperature: 0.8,
          maxTokens: 600,
          model: session.ai_model || 'openai/gpt-4-turbo-preview'
        }
      );

      // Store voice message and response
      await supabase
        .from('ai_chat_messages')
        .insert([
          {
            session_id: sessionId,
            role: 'user',
            content: processedTranscription,
            message_type: 'voice_transcript',
            metadata: messageData.metadata
          },
          {
            session_id: sessionId,
            role: 'assistant',
            content: aiResponse.content,
            message_type: 'voice_response',
            confidence_score: 0.9
          }
        ]);

      logger.info('Voice message processed', {
        userId,
        sessionId,
        dialect,
        transcriptionLength: processedTranscription.length
      });

      res.json({
        success: true,
        message: 'Voice message processed successfully',
        data: {
          sessionId,
          transcription: processedTranscription,
          aiResponse: aiResponse.content,
          voiceMetadata: {
            dialect,
            processingMethod: 'enhanced_ai',
            culturalAdaptation: true
          }
        },
      });

    } catch (error) {
      logger.error('Failed to process voice message', { error, sessionId, userId });
      throw createError.internalServerError('Failed to process voice message');
    }
  })
);

/**
 * @swagger
 * /api/ai/v2/conversation/{sessionId}/image:
 *   post:
 *     summary: Analyze image in AI conversation (portfolio, work samples)
 *     tags: [Phase 3 AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - imageFile
 *             properties:
 *               imageFile:
 *                 type: string
 *                 format: binary
 *               description:
 *                 type: string
 *                 description: User description of the image
 *               analysisType:
 *                 type: string
 *                 enum: [portfolio, work_sample, skill_demonstration, certificate]
 *                 default: portfolio
 *     responses:
 *       200:
 *         description: Image analyzed successfully
 */
router.post(
  '/v2/conversation/:sessionId/image',
  authenticate,
  [
    param('sessionId')
      .isUUID()
      .withMessage('Session ID must be a valid UUID'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { description = '', analysisType = 'portfolio' } = req.body;
    const userId = req.user!.id;

    try {
      // Get session from Supabase
      const { data: session, error: sessionError } = await supabase
        .from('ai_chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single();

      if (sessionError || !session) {
        throw createError.notFound('Conversation session not found');
      }

      // For now, simulate image analysis
      // In production, you would integrate with GPT-4 Vision or similar service
      const simulatedImageAnalysis = `تم تحليل الصورة المرفقة (${analysisType}). ${description ? `وصف المستخدم: ${description}` : ''}

نتائج التحليل المحاكي:
- نوع المحتوى: ${analysisType === 'portfolio' ? 'عمل من الأعمال السابقة' : 'عينة عمل'}
- الجودة المقدرة: عالية
- المهارات المستنتجة: تصميم، إبداع، احترافية
- التوصيات: ممتاز للعرض في الملف الشخصي`;

      // Create enhanced system prompt for image analysis
      const imageAnalysisPrompt = `أنت خبير في تحليل الأعمال والمشاريع للمستخدمين السوريين على منصة فريلا سوريا.

تحليل الصورة:
${simulatedImageAnalysis}

نوع التحليل: ${analysisType}
وصف المستخدم: ${description || 'لم يتم تقديم وصف'}

مهمتك:
1. تحليل العمل المعروض وتقييم جودته
2. استخراج المهارات الظاهرة في العمل
3. تقديم نصائح لتحسين العرض
4. اقتراح كيفية استخدام هذا العمل في الملف الشخصي
5. تقدير السعر المناسب لهذا النوع من الأعمال في السوق السوري

اكتب تحليلاً مفصلاً ومفيداً باللغة العربية مع مراعاة السوق السوري.`;

      const aiResponse = await openRouterService.chatCompletion(
        [{ role: 'user', content: imageAnalysisPrompt }],
        {
          userId: session.user_id,
          sessionId: session.id,
          userRole: session.user_role,
          language: session.language,
          currentStep: session.current_step,
          extractedData: session.extracted_data
        },
        {
          temperature: 0.6,
          maxTokens: 800,
          model: 'openai/gpt-4-turbo-preview' // Use vision model in production
        }
      );

      // Extract skills and insights from image analysis
      const skillExtractionPrompt = `
بناءً على تحليل الصورة التالي، استخرج المعلومات بصيغة JSON:

التحليل: ${aiResponse.content}

استخرج:
{
  "detected_skills": [],
  "quality_score": 0,
  "recommended_price_range": {"min": 0, "max": 0, "currency": "USD"},
  "portfolio_category": "",
  "improvement_suggestions": [],
  "market_competitiveness": 0
}`;

      const extractionResponse = await openRouterService.chatCompletion(
        [{ role: 'user', content: skillExtractionPrompt }],
        {},
        {
          temperature: 0.3,
          maxTokens: 400,
          model: 'openai/gpt-4-turbo-preview'
        }
      );

      let extractedData = {};
      try {
        extractedData = JSON.parse(extractionResponse.content);
      } catch (parseError) {
        logger.warn('Failed to parse image analysis data', { parseError, sessionId });
      }

      // Store image analysis messages
      await supabase
        .from('ai_chat_messages')
        .insert([
          {
            session_id: sessionId,
            role: 'user',
            content: `تم رفع صورة (${analysisType}): ${description}`,
            message_type: 'image_upload',
            metadata: {
              imageData: {
                analysisType,
                description,
                simulatedAnalysis: simulatedImageAnalysis
              }
            }
          },
          {
            session_id: sessionId,
            role: 'assistant',
            content: aiResponse.content,
            message_type: 'image_analysis',
            confidence_score: 0.85,
            extracted_data: extractedData
          }
        ]);

      // Update session with image analysis data
      const updatedExtractedData = {
        ...session.extracted_data,
        portfolio_analysis: {
          ...session.extracted_data?.portfolio_analysis,
          [analysisType]: extractedData
        },
        last_image_analysis: new Date().toISOString()
      };

      await supabase
        .from('ai_chat_sessions')
        .update({
          extracted_data: updatedExtractedData,
          last_activity: new Date().toISOString()
        })
        .eq('id', sessionId);

      logger.info('Image analyzed successfully', {
        userId,
        sessionId,
        analysisType,
        extractedSkills: (extractedData as any).detected_skills?.length || 0
      });

      res.json({
        success: true,
        message: 'Image analyzed successfully',
        data: {
          sessionId,
          analysis: aiResponse.content,
          extractedData,
          imageMetadata: {
            analysisType,
            description,
            processingMethod: 'ai_vision_simulation'
          },
          recommendations: {
            portfolioUsage: 'يمكن استخدام هذا العمل في الملف الشخصي',
            pricingSuggestion: (extractedData as any).recommended_price_range || null,
            improvementTips: (extractedData as any).improvement_suggestions || []
          }
        },
      });

    } catch (error) {
      logger.error('Failed to analyze image', { error, sessionId, userId });
      throw createError.internalServerError('Failed to analyze image');
    }
  })
);

// Debug endpoint for AI services
router.get('/debug/services', asyncHandler(async (req, res) => {
  try {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      openrouter: {
        apiKey: process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured',
        keyLength: process.env.OPENROUTER_API_KEY?.length || 0
      },
      supabase: {
        url: process.env.SUPABASE_URL ? 'configured' : 'not configured',
        serviceKey: process.env.SUPABASE_SERVICE_KEY ? 'configured' : 'not configured'
      },
      services: {
        aiConversationService: 'loaded',
        phase3AIService: 'loaded'
      }
    };

    res.json({
      success: true,
      message: 'AI services debug info',
      data: debugInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Debug endpoint error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

export default router;
