{
  message: 'Test log',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:24:38'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:51:22'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:14'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:23'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:53:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:10:59'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:11:20'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:13:53'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:47'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:58'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:06'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:07'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '4095ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:33'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:39'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /favicon.ico not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/favicon.ico',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  duration: '9ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '23ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '27ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '67ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:42'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:43'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:43'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:05'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:05'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:51'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:57'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:58'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '4079ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:16'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /favicon.ico not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/favicon.ico',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  duration: '9ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '6ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '20ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '21ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '51ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:19'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:19'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:30'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:31'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '59ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:58:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:13'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:20'
}
{
  service: 'freela-api',
  environment: 'development',
  source: 'body',
  errors: [
    {
      field: 'password',
      message: 'Password must contain at least one uppercase letter',
      code: 'invalid_string'
    },
    {
      field: 'password',
      message: 'Password must contain at least one special character',
      code: 'invalid_string'
    },
    { field: 'acceptTerms', message: 'Required', code: 'invalid_type' }
  ],
  data: {
    email: '<EMAIL>',
    role: 'CLIENT',
    password: 'password123',
    firstName: 'Test',
    lastName: 'User'
  },
  endpoint: '/register',
  method: 'POST',
  level: 'warn',
  message: 'Validation failed',
  timestamp: '2025-06-10 15:59:27'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/register',
  statusCode: 400,
  duration: '6ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:27'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'user_registered',
  userId: 'cmbql82nc0000fh9zkti45s42',
  details: { email: '<EMAIL>', role: 'CLIENT', language: 'ar' },
  timestamp: '2025-06-10 15:59:45',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/register',
  statusCode: 201,
  duration: '385ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:45'
}
{
  service: 'freela-api',
  environment: 'development',
  userId: 'cmbqkq8ab0000wvkt45ce5wz6',
  sessionId: 'Ds8zroeacLM-bKqxkxY-i',
  level: 'info',
  message: 'Session created',
  timestamp: '2025-06-10 15:59:54'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'user_logged_in',
  userId: 'cmbqkq8ab0000wvkt45ce5wz6',
  details: {
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
    rememberMe: false
  },
  timestamp: '2025-06-10 15:59:54',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/login',
  statusCode: 200,
  duration: '330ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:54'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '28ms',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 16:13:32'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:20'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:20'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '10ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:27:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '40ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:27:06'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:18'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:18'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '10ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 21:39:09'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:03'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:03'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:14'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:14'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:24'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:24'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-13 01:23:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:29'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:31'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:33'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:33'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:34'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:34'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:35'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:36'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:36'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:37'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-13 01:23:38'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 01:23:38'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:40:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '93ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:10'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /favicon.ico not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/favicon.ico',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-13 22:45:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  duration: '18ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '9ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '22ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '59ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:45:16'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:48:26'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:50:42'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-13 22:51:49'
}
{
  message: 'SIGINT received, shutting down gracefully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:53:07'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:00'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:01'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 22:58:04'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:56'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:13:57'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-13 23:32:23'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 00:58:27'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:28'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:28'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:29'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:24:29'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route POST /api/ai/conversation/start not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/ai/conversation/start',
  method: 'POST',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-14 01:25:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/api/ai/conversation/start',
  statusCode: 404,
  duration: '112ms',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 01:25:29'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:223:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:32:57'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:223:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:33:44'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:223:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:223:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:37'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:38'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:38'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:38'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:39'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:39'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:39'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:57:48'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '12ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 18:58:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '46ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 18:58:24'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '43ms',
  ip: '::1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 18:59:30'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 18:59:48'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:06'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:06'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '🔌 WebSocket service initialized',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 19:00:07'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:11'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:38:12'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:39:12'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:40:27'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:42:22'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:00'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:00'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:00'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '626ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 21:45:02'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:42'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:42'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:42'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:43'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:43'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:45:43'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '679ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-14 21:45:44'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1/test-connection',
  statusCode: 200,
  duration: '0ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-14 21:45:44'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 21:46:58'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: 'Swagger documentation available at http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: 'Attempting to start server on port 3005...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '🚀 Server running on port 3005',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '📚 API Documentation: http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '🏥 Health Check: http://localhost:3005/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:32:36'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: 'Swagger documentation available at http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: 'Attempting to start server on port 3005...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3005 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '🚀 Server running on port 3005',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '📚 API Documentation: http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '🏥 Health Check: http://localhost:3005/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:52'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:53'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-14 22:33:53'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 22:33:53'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T20:33:53.894Z","uptime":3.1752733,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-14 22:33:53'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:55'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:55'
}
{
  message: 'Swagger documentation available at http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: 'Attempting to start server on port 3005...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3005 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '🚀 Server running on port 3005',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '📚 API Documentation: http://localhost:3005/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '🏥 Health Check: http://localhost:3005/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:56'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '7ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-14 23:54:57'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 23:54:57'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T21:54:57.217Z","uptime":3.6750977,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-14 23:54:57'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:39'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:40'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:02:40'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:02:40'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:02:40.988Z","uptime":3.3715775,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:02:40'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:03:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1',
  statusCode: 200,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:03:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1/test-connection',
  statusCode: 200,
  duration: '0ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:03:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/conversation/start',
  statusCode: 401,
  duration: '0ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:03:02'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /api/v1/ai/v2/conversation/start not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/v1/ai/v2/conversation/start',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-15 00:03:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1/ai/v2/conversation/start',
  statusCode: 404,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:03:02'
}
{
  message: 'SIGINT received, shutting down gracefully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:05:00'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:01'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '12ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:10:02'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:02'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:10:02.653Z","uptime":3.7480741,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:10:02'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:46'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:46'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:46'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:47'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:48'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '654ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:10:48'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:10:48'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:10:48.929Z","uptime":3.849733,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:10:48'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:21'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:22'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '9ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:11:22'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:11:22'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:11:22.970Z","uptime":3.0319233,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:11:22'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:15'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:15'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:16'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '594ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:12:18'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:12:18'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:12:18.338Z","uptime":3.616566,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:12:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '776ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:12:37'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '623ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:12:53'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1/test-connection',
  statusCode: 200,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:13:02'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route POST /api/onboarding/user-data not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/onboarding/user-data',
  method: 'POST',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-15 00:53:25'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/api/onboarding/user-data',
  statusCode: 404,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:53:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'jwt malformed',
  level: 'warn',
  message: 'Invalid access token',
  timestamp: '2025-06-15 00:53:36'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_token_attempt',
  details: { token: '11819772165128424962...' },
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  timestamp: '2025-06-15 00:53:36',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '5ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:53:36'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:56:59'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:56:59'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:56:59'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:00'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '677ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:57:01'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 00:57:01'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T22:57:01.708Z","uptime":4.3696184,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 00:57:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/debug/services',
  statusCode: 200,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:57:21'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '656ms',
  ip: '127.0.0.1',
  userAgent: 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:57:52'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/debug/services',
  statusCode: 200,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:57:52'
}
{
  service: 'freela-api',
  environment: 'development',
  error: {
    code: '22P02',
    details: null,
    hint: null,
    message: 'invalid input syntax for type uuid: "test-user-123"'
  },
  userIdHeader: 'test-user-123',
  level: 'warn',
  message: 'Direct user ID authentication failed',
  timestamp: '2025-06-15 00:57:52'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '648ms',
  ip: '127.0.0.1',
  userAgent: 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:57:52'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route POST /api/onboarding/user-data not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/onboarding/user-data',
  method: 'POST',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-15 00:59:09'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/api/onboarding/user-data',
  statusCode: 404,
  duration: '4ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:59:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'jwt malformed',
  level: 'warn',
  message: 'Invalid access token',
  timestamp: '2025-06-15 00:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_token_attempt',
  details: { token: '11819772165128424962...' },
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  timestamp: '2025-06-15 00:59:12',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 00:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route POST /api/onboarding/user-data not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/onboarding/user-data',
  method: 'POST',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-15 01:02:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/api/onboarding/user-data',
  statusCode: 404,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:02:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'jwt malformed',
  level: 'warn',
  message: 'Invalid access token',
  timestamp: '2025-06-15 01:02:05'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_token_attempt',
  details: { token: '11819772165128424962...' },
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  timestamp: '2025-06-15 01:02:05',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:02:05'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:6543`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:00'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '21ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:08:01'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:08:01'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:08:01.808Z","uptime":5.0895614,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:08:01'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:50'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:50'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:51'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:52'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '14ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:09:52'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:09:52'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:09:52.143Z","uptime":3.2326477,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:09:52'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:43'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:10:44'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:10:44.471Z","uptime":3.2140437,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:11:28',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '12ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:11:28'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:11:39',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:11:39'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'e36AOLIOqufFkNsPmeP3Q',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:11:53',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '32ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:11:53'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:17'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '11ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:18'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:12:18'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:12:18.406Z","uptime":3.3952083,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:12:18'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route POST /api/onboarding/user-data not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/api/onboarding/user-data',
  method: 'POST',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-15 01:12:20'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/api/onboarding/user-data',
  statusCode: 404,
  duration: '4ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:20'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_YW1lcmthbGxham9v',
  details: {
    email: '<EMAIL>',
    isNewUser: true,
    provider: 'google'
  },
  timestamp: '2025-06-15 01:12:23',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '7ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:23'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'hY4tPHfA0s-HaBmJwKVks',
    userId: 'user_YW1lcmthbGxham9v'
  },
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  timestamp: '2025-06-15 01:12:23',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'node',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:23'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:12:29',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:29'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'nE6L0D_CRHZf1iv7OB7Be',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:12:42',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:12:42'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:14'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:15:15'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:15'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:15:15.812Z","uptime":3.4430373,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:15:15'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:31'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:32'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:15:32'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:15:32'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:15:32.492Z","uptime":3.0338265,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:15:32'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:15:43',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '7ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:15:43'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'oHEPwp7L60_BJWi1vIw8M',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:16:01',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '4ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:16:01'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:05'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:05'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:06'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:07'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:17:07'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:17:07'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:17:07.099Z","uptime":3.1897406,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:17:07'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'oHEPwp7L60_BJWi1vIw8M',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:18:06',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '4ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:18:06'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:35'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:35'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:36'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:37'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:18:37'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:18:37'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:18:37.112Z","uptime":3.2338291,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:18:37'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:18:49',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:18:49'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: '25ngvmuKouk2eMGIhaNUw',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:19:13',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:19:13'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:39'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:39'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:40'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:40'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:40'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:40'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:41'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:41'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:42'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:42'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:43'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:43'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:44'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:44'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:45'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:46'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '15ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:20:46'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:46'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:20:46.081Z","uptime":8.8881793,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:20:46'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:57'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:58'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:58'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:20:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:20:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:00'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '19ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:21:01'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:01'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:21:01.865Z","uptime":8.9761748,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:21:01'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:19'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:19'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:21:19'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:20'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:20'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:21'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:21'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:21'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:21'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:22'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:22'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:22'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:23'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:23'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:24'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:24'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:25'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:26'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '14ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:21:26'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:26'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:21:26.587Z","uptime":9.2262419,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:21:26'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:32'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:32'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:33'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:33'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:33'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:34'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:34'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:35'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:35'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:36'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:36'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:37'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:38'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '19ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:21:38'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:38'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:21:38.912Z","uptime":9.0647985,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:21:38'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:48'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:48'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:49'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:49'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:50'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:50'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:50'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:50'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:51'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:52'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:52'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:53'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:53'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:54'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:55'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '50ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:21:55'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:21:55'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:21:55.145Z","uptime":9.3938039,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:21:55'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:225:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:06'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:07'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:08'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:09'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:10'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '13ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:22:10'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-15 01:22:10'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-14T23:22:10.057Z","uptime":9.43743,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-15 01:22:10'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'nextauth_login_successful',
  userId: 'user_dGVzdEBleGFtcGxl',
  details: { email: '<EMAIL>', isNewUser: true, provider: 'google' },
  timestamp: '2025-06-15 01:22:16',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/nextauth/login',
  statusCode: 200,
  duration: '6ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:22:16'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'invalid_session_attempt',
  details: {
    sessionId: 'qT5SdiWTLcdFf6kD4wXmi',
    userId: 'user_dGVzdEBleGFtcGxl'
  },
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  timestamp: '2025-06-15 01:22:44',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/v2/conversation/start',
  statusCode: 401,
  duration: '4ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-15 01:22:44'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:01'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:06'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:07'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:15:08'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:15:08.833Z","uptime":8.8244563,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 19:15:47'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 19:15:47',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '1389ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:15:47'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '57ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:15:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:03'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:03'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:04'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:04'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '5ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:04'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '65ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:04'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:16:05'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:06'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:07'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:08'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:09'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:31:09'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:31:09'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:31:09.242Z","uptime":8.7845464,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:31:09'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:01'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:32:02'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:32:02'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:32:02.432Z","uptime":3.041007,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:32:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '36ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:32:34'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 19:32:35'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 19:32:35',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '848ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:32:35'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:34:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:34:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:34:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:34:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:34:36'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:28'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:29'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:30'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '9ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:41:30'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:41:30'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:41:30.249Z","uptime":3.2377098,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:41:30'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:07'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '10ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:44:08'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:44:08'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:44:08.848Z","uptime":3.1834114,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:44:08'
}
{
  message: '⚠️ Supabase configuration missing, creating mock client',
  level: 'warn',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:02'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:03'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 19:49:03'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 19:49:03'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T17:49:03.362Z","uptime":3.1396364,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 19:49:03'
}
{
  message: 'Logger test message',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:08:40'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 20:08:41'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 20:09:06'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:07'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:09:08'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:09:08'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T18:09:08.246Z","uptime":3.0553305,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 20:09:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '40ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:09:46'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:10:02'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:10:02',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '930ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:10:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/api/v1/test-connection',
  statusCode: 200,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:10:37'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.bio does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:11:53'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:11:53',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '899ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:11:53'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.total_reviews does not exist',
  level: 'error',
  timestamp: '2025-06-18 20:19:35'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 20:19:35',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '854ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:19:35'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:35'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '11ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:21:36'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:21:36'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T18:21:36.838Z","uptime":3.1220487,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 20:21:36'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:45'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:46'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:44:46'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:44:46'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T18:44:46.844Z","uptime":3.0013382,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 20:44:46'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 20:46:18'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`
  
  Please make sure your database server is running at `db.bivignfixaqrmdcbsnqh.supabase.co:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:233:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:46:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:19'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:20'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '8ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 20:46:20'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 20:46:20'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T18:46:20.316Z","uptime":2.9630945,"environment":"development","version":"v1","services":{"database":{"status":"error"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 20:46:20'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:19:56'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:56'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:56'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:57'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:58'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '615ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:19:59'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:19:59'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:19:59.084Z","uptime":4.5292726,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:19:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '653ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:20:25'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column expert_profiles_1.total_reviews does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:20:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:20:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '682ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:20:25'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:22:34'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:35'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:35'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:36'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:37'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '603ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:22:37'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:37'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:22:37.715Z","uptime":4.5676146,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:22:37'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:22:48'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:48'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:48'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:49'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:50'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '652ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:22:50'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:22:50'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:22:50.902Z","uptime":4.6606192,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:22:50'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:23:00'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:00'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:00'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:01'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '950ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:23:03'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:03'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:23:03.652Z","uptime":5.2441496,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:23:03'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:23:14'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:14'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:14'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:15'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:16'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '847ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:23:17'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:17'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:23:17.639Z","uptime":4.8003254,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:23:17'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:23:34'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:34'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:34'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:35'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:36'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '659ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:23:37'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:37'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:23:37.296Z","uptime":4.6236827,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:23:37'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:23:46'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:46'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:46'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:47'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:48'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '653ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:23:49'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:49'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:23:49.247Z","uptime":4.4938795,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:23:49'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:23:57'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:58'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:23:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '643ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:24:00'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:24:00'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:24:00.565Z","uptime":4.4219602,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:24:00'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching services: column services.is_active does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:24:29'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:24:29',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search services error: Failed to search services',
  stack: 'AppError: Failed to search services\n' +
    '    at searchServices (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\services.ts:281:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '809ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:24:29'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:34:26'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:27'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:27'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:28'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '642ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:34:29'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:29'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:34:29.885Z","uptime":4.6626383,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:34:29'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:34:40'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:40'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:40'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:41'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:42'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '635ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:34:43'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:43'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:34:43.127Z","uptime":4.3478868,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:34:43'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:34:52'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:52'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:52'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:53'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:54'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '659ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:34:55'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:34:55'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:34:55.077Z","uptime":4.4402265,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:34:55'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:35:05'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:05'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:05'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:06'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:07'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '636ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:35:07'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:35:07'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:35:07.757Z","uptime":4.3353482,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:35:07'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '680ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:35:25'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '42703',
  details: null,
  hint: null,
  message: 'Error searching experts: column expert_profiles.is_available does not exist',
  level: 'error',
  timestamp: '2025-06-18 21:35:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:35:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:288:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '602ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:35:25'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:36:21'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:21'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:21'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:22'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:23'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '738ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:36:24'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:24'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:36:24.056Z","uptime":4.6467551,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:36:24'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:36:33'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:33'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:33'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:34'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:35'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '634ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:36:36'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:36'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:36:36.286Z","uptime":4.360957,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:36:36'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:36:46'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:46'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:46'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:47'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:48'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '683ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:36:49'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:36:49'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:36:49.279Z","uptime":4.3984137,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:36:49'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '757ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:37:27'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 21:37:27'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:37:27',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '304ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:37:27'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:51:30'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:30'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:30'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:31'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:32'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '700ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:51:33'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:33'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:51:33.260Z","uptime":4.6686539,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:51:33'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:51:45'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:45'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:45'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:46'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:47'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '599ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:51:47'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:47'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:51:47.844Z","uptime":4.2136187,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:51:47'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 21:51:59'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:59'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:51:59'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:00'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '668ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:52:02'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 21:52:02'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T19:52:02.433Z","uptime":4.7389648,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 21:52:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '629ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:52:20'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 21:52:20'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 21:52:20',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '232ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 21:52:20'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:03:03'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:04'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:04'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:05'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:06'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '970ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:03:07'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:03:07'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:03:07.226Z","uptime":5.0619437,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:03:07'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '641ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:03:25'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:03:25'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:03:25',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:291:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '575ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:03:25'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:04:39'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:39'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:39'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:40'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '733ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:04:42'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:42'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:04:42.183Z","uptime":4.7088997,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:04:42'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:04:55'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:56'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '645ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:04:58'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:04:58'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:04:58.496Z","uptime":4.2674272,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:04:58'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:05:12'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:12'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:12'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:13'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:14'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '668ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:05:15'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:05:15'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:05:15.291Z","uptime":4.3621651,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:05:15'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:06:12'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:12'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:13'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:14'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '672ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:06:15'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:15'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:06:15.491Z","uptime":4.4220881,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:06:15'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:06:27'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:27'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:27'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:28'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '679ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:06:30'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:30'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:06:30.174Z","uptime":4.6445482,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:06:30'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:06:56'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:57'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:58'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '637ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:06:59'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:06:59'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:06:59.355Z","uptime":4.1486179,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:06:59'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:07:13'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:13'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:14'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:15'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '630ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:07:16'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:16'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:07:16.395Z","uptime":4.5756547,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:07:16'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:26'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:27'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '325ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:07:28'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:28'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:07:28.011Z","uptime":3.7082216,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:07:28'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:07:39'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:39'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:39'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:41'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:42'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '979ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:07:43'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:43'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:07:43.026Z","uptime":5.4011963,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:07:43'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:07:55'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:55'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:56'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '647ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:07:57'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:07:57'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:07:57.992Z","uptime":4.0766491,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:07:57'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '695ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:08:24'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:08:24'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:08:24',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '243ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:08:24'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:09:08'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:09'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:10'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '686ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:09:11'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:11'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:09:11.407Z","uptime":4.2595858,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:09:11'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:09:20'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:21'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:22'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '605ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:09:23'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:09:23'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:09:23.429Z","uptime":4.6166484,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:09:23'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '763ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:09:39'
}
{
  service: 'freela-api',
  environment: 'development',
  code: '22P02',
  details: 'Token "AVAILABLE" is invalid.',
  hint: null,
  message: 'Error searching experts: invalid input syntax for type json',
  level: 'error',
  timestamp: '2025-06-18 22:09:40'
}
{
  service: 'freela-api',
  environment: 'development',
  statusCode: 500,
  type: 'INTERNAL',
  severity: 'MEDIUM',
  isOperational: true,
  timestamp: '2025-06-18 22:09:40',
  messageAr: undefined,
  details: undefined,
  path: undefined,
  method: undefined,
  userId: undefined,
  requestId: undefined,
  name: 'AppError',
  level: 'error',
  message: 'Search experts error: Failed to search experts',
  stack: 'AppError: Failed to search experts\n' +
    '    at searchExperts (C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\controllers\\experts.ts:292:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 500,
  duration: '582ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:09:40'
}
{
  service: 'freela-api',
  environment: 'development',
  url: 'https://bivignfixaqrmdcbsnqh.supabase.co',
  hasServiceKey: true,
  level: 'info',
  message: '✅ Supabase client initialized successfully',
  timestamp: '2025-06-18 22:10:42'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:42'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:42'
}
{
  message: 'Redis: Disabled in configuration, skipping connection',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: 'Attempting to start server on port 3001...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  service: 'freela-api',
  environment: 'development',
  address: { address: '0.0.0.0', family: 'IPv4', port: 3001 },
  level: 'info',
  message: '✅ Server is actually listening on:',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: '🔌 WebSocket service temporarily disabled',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:43'
}
{
  message: 'Testing server connectivity...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '674ms',
  ip: '127.0.0.1',
  userAgent: undefined,
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:10:44'
}
{
  message: '✅ Server connectivity test passed - Status: 200',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-18 22:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  data: '{"status":"ok","timestamp":"2025-06-18T20:10:44.843Z","uptime":4.5090255,"environment":"development","version":"v1","services":{"database":{"status":"ok"},"redis":{"status":"error"}}}',
  level: 'info',
  message: 'Health response:',
  timestamp: '2025-06-18 22:10:44'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '813ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:11:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '546ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:11:03'
}
{
  service: 'freela-api',
  environment: 'development',
  event: 'sql_injection_attempt',
  details: {
    query: { query: 'test' },
    ip: '127.0.0.1',
    endpoint: '/api/v1/search'
  },
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  timestamp: '2025-06-18 22:11:03',
  level: 'warn',
  message: 'Security Event'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '298ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:12:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '544ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:12:02'
}
{
  service: 'freela-api',
  environment: 'development',
  source: 'query',
  errors: [ { field: 'q', message: 'Required', code: 'invalid_type' } ],
  data: {},
  endpoint: '/',
  method: 'GET',
  level: 'warn',
  message: 'Validation failed',
  timestamp: '2025-06-18 22:12:02'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 400,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'axios/1.10.0',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-18 22:12:02'
}
