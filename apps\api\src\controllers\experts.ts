import { Request, Response } from 'express';
import { supabaseAdmin } from '@freela/database/src/supabase';
import { logger } from '../utils/logger';
import { AppError } from '../utils/errors';

// Expert profile data mapping utilities
const mapToSupabaseExpert = (data: any) => {
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'userId':
        mapped.user_id = value;
        break;
      case 'hourlyRate':
        mapped.hourly_rate = value;
        break;
      case 'totalReviews':
        mapped.review_count = value;
        break;
      case 'completedProjects':
        mapped.completed_projects = value;
        break;
      case 'isAvailable':
        mapped.availability = value ? { status: 'AVAILABLE' } : { status: 'UNAVAILABLE' };
        break;
      case 'isVerified':
        mapped.is_verified = value;
        break;
      case 'serviceAreas':
        mapped.service_areas = value;
        break;
      case 'socialLinks':
        mapped.social_links = value;
        break;
      case 'verificationNotes':
        mapped.verification_notes = value;
        break;
      case 'createdAt':
        mapped.created_at = value;
        break;
      case 'updatedAt':
        mapped.updated_at = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

const mapFromSupabaseExpert = (data: any) => {
  if (!data) return data;
  
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'user_id':
        mapped.userId = value;
        break;
      case 'hourly_rate':
        mapped.hourlyRate = value;
        break;
      case 'review_count':
        mapped.totalReviews = value;
        break;
      case 'completed_projects':
        mapped.completedProjects = value;
        break;
      case 'availability':
        mapped.isAvailable = value && typeof value === 'object' && (value as any)?.status === 'AVAILABLE';
        break;
      case 'is_verified':
        mapped.isVerified = value;
        break;
      case 'service_areas':
        mapped.serviceAreas = value;
        break;
      case 'social_links':
        mapped.socialLinks = value;
        break;
      case 'verification_notes':
        mapped.verificationNotes = value;
        break;
      case 'created_at':
        mapped.createdAt = value;
        break;
      case 'updated_at':
        mapped.updatedAt = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

/**
 * Create expert profile
 */
export const createExpertProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Check if expert profile already exists
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('expert_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingProfile) {
      throw new AppError('Expert profile already exists', 400);
    }

    const profileData = {
      ...req.body,
      userId,
      rating: 0,
      totalReviews: 0,
      completedProjects: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseExpert(profileData);

    const { data: profile, error } = await supabaseAdmin
      .from('expert_profiles')
      .insert(supabaseData)
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating expert profile:', error);
      throw new AppError('Failed to create expert profile', 500);
    }

    const mappedProfile = mapFromSupabaseExpert(profile);

    logger.info('Expert profile created successfully', { 
      profileId: profile.id, 
      userId 
    });

    res.status(201).json({
      success: true,
      message: 'Expert profile created successfully',
      data: mappedProfile
    });
  } catch (error) {
    logger.error('Create expert profile error:', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Search and list experts with filters and pagination
 */
export const searchExperts = async (req: Request, res: Response) => {
  try {
    const {
      q,
      skills,
      category,
      governorate,
      city,
      availability,
      minRating,
      maxHourlyRate,
      minExperience,
      languages,
      isAvailable,
      isVerified,
      sortBy = 'rating',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = req.query;

    let query = supabaseAdmin
      .from('expert_profiles')
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' });

    // Apply filters
    if (isAvailable !== undefined) {
      const isAvailableBoolean = String(isAvailable) === 'true';
      if (isAvailableBoolean) {
        query = query.eq('availability->status', 'AVAILABLE');
      } else {
        query = query.neq('availability->status', 'AVAILABLE');
      }
    }

    if (isVerified !== undefined) {
      query = query.eq('is_verified', isVerified);
    }

    // Note: availability filtering is handled by isAvailable parameter above
    // The availability parameter could be used for more complex JSONB queries if needed

    if (minRating) {
      query = query.gte('rating', minRating);
    }

    if (maxHourlyRate) {
      query = query.lte('hourly_rate', maxHourlyRate);
    }

    if (minExperience) {
      query = query.gte('experience', minExperience);
    }

    // Location filters
    if (governorate) {
      query = query.contains('location', { governorate });
    }

    if (city) {
      query = query.contains('location', { city });
    }

    // Skills search
    if (skills) {
      const skillArray = skills.toString().split(',').map(skill => skill.trim());
      query = query.overlaps('skills', skillArray);
    }

    // Languages search
    if (languages) {
      const langArray = languages.toString().split(',').map(lang => lang.trim());
      query = query.overlaps('languages', langArray);
    }

    // Text search
    if (q) {
      query = query.or(`bio.ilike.%${q}%,skills.cs.{${q}}`);
    }

    // Sorting
    const sortField = sortBy === 'hourly_rate' ? 'hourly_rate' :
                     sortBy === 'total_reviews' ? 'review_count' :
                     sortBy === 'created_at' ? 'created_at' :
                     sortBy === 'experience' ? 'experience' :
                     'rating';
    
    query = query.order(sortField, { ascending: sortOrder === 'asc' });

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    const { data: experts, error, count } = await query;

    if (error) {
      logger.error('Error searching experts:', error);
      throw new AppError('Failed to search experts', 500);
    }

    const mappedExperts = experts?.map(mapFromSupabaseExpert) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Experts retrieved successfully',
      data: {
        experts: mappedExperts,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Search experts error:', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get expert profile by ID
 */
export const getExpertById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const { data: expert, error } = await supabaseAdmin
      .from('expert_profiles')
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        ),
        services(
          id,
          title,
          category,
          base_price,
          status
        )
      `)
      .eq('id', id)
      .single();

    if (error || !expert) {
      throw new AppError('Expert profile not found', 404);
    }

    const mappedExpert = mapFromSupabaseExpert(expert);

    res.json({
      success: true,
      message: 'Expert profile retrieved successfully',
      data: mappedExpert
    });
  } catch (error) {
    logger.error('Get expert by ID error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Update expert profile
 */
export const updateExpertProfile = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get the expert profile to check ownership
    const { data: existingProfile, error: fetchError } = await supabaseAdmin
      .from('expert_profiles')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError || !existingProfile) {
      throw new AppError('Expert profile not found', 404);
    }

    // Check if user owns the profile or is admin
    if (userRole !== 'ADMIN' && existingProfile.user_id !== userId) {
      throw new AppError('Access denied - you can only update your own profile', 403);
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseExpert(updateData);

    const { data: updatedProfile, error } = await supabaseAdmin
      .from('expert_profiles')
      .update(supabaseData)
      .eq('id', id)
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error updating expert profile:', error);
      throw new AppError('Failed to update expert profile', 500);
    }

    const mappedProfile = mapFromSupabaseExpert(updatedProfile);

    logger.info('Expert profile updated successfully', {
      profileId: id,
      userId
    });

    res.json({
      success: true,
      message: 'Expert profile updated successfully',
      data: mappedProfile
    });
  } catch (error) {
    logger.error('Update expert profile error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get current user's expert profile
 */
export const getMyExpertProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const { data: expert, error } = await supabaseAdmin
      .from('expert_profiles')
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        ),
        services(
          id,
          title,
          category,
          base_price,
          status
        )
      `)
      .eq('user_id', userId)
      .single();

    if (error || !expert) {
      throw new AppError('Expert profile not found', 404);
    }

    const mappedExpert = mapFromSupabaseExpert(expert);

    res.json({
      success: true,
      message: 'Expert profile retrieved successfully',
      data: mappedExpert
    });
  } catch (error) {
    logger.error('Get my expert profile error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Update current user's expert profile
 */
export const updateMyExpertProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get the expert profile
    const { data: existingProfile, error: fetchError } = await supabaseAdmin
      .from('expert_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingProfile) {
      throw new AppError('Expert profile not found', 404);
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseExpert(updateData);

    const { data: updatedProfile, error } = await supabaseAdmin
      .from('expert_profiles')
      .update(supabaseData)
      .eq('user_id', userId)
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error updating my expert profile:', error);
      throw new AppError('Failed to update expert profile', 500);
    }

    const mappedProfile = mapFromSupabaseExpert(updatedProfile);

    logger.info('Expert profile updated successfully', {
      profileId: existingProfile.id,
      userId
    });

    res.json({
      success: true,
      message: 'Expert profile updated successfully',
      data: mappedProfile
    });
  } catch (error) {
    logger.error('Update my expert profile error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Verify expert profile (Admin only)
 */
export const verifyExpert = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { isVerified, verificationNotes } = req.body;

    const updateData = {
      isVerified,
      verificationNotes,
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseExpert(updateData);

    const { data: updatedProfile, error } = await supabaseAdmin
      .from('expert_profiles')
      .update(supabaseData)
      .eq('id', id)
      .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error verifying expert profile:', error);
      throw new AppError('Failed to update verification status', 500);
    }

    if (!updatedProfile) {
      throw new AppError('Expert profile not found', 404);
    }

    const mappedProfile = mapFromSupabaseExpert(updatedProfile);

    logger.info('Expert verification status updated', {
      profileId: id,
      isVerified,
      adminId: req.user?.id
    });

    res.json({
      success: true,
      message: `Expert ${isVerified ? 'verified' : 'unverified'} successfully`,
      data: mappedProfile
    });
  } catch (error) {
    logger.error('Verify expert error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

