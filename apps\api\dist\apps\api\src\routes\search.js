"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const searchController = __importStar(require("../controllers/search"));
const zod_1 = require("zod");
const router = (0, express_1.Router)();
// Validation schemas for search
const searchServicesSchema = zod_1.z.object({
    q: zod_1.z.string().optional(), // Search query
    category: zod_1.z.string().optional(),
    subcategory: zod_1.z.string().optional(),
    serviceType: zod_1.z.enum(['DIGITAL', 'PHYSICAL']).optional(),
    priceType: zod_1.z.enum(['FIXED', 'HOURLY', 'NEGOTIABLE']).optional(),
    minPrice: zod_1.z.number().min(0).optional(),
    maxPrice: zod_1.z.number().min(0).optional(),
    governorate: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    tags: zod_1.z.string().optional(), // Comma-separated tags
    skills: zod_1.z.string().optional(), // Comma-separated skills for expert search
    minRating: zod_1.z.number().min(0).max(5).optional(),
    maxDistance: zod_1.z.number().min(0).optional(), // For location-based search
    lat: zod_1.z.number().optional(), // User's latitude
    lng: zod_1.z.number().optional(), // User's longitude
    sortBy: zod_1.z.enum(['price', 'rating', 'distance', 'created_at', 'popularity']).default('rating'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20),
    isActive: zod_1.z.boolean().default(true)
});
const searchExpertsSchema = zod_1.z.object({
    q: zod_1.z.string().optional(), // Search query
    skills: zod_1.z.string().optional(), // Comma-separated skills
    category: zod_1.z.string().optional(),
    governorate: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    availability: zod_1.z.enum(['FULL_TIME', 'PART_TIME', 'WEEKENDS', 'FLEXIBLE']).optional(),
    minRating: zod_1.z.number().min(0).max(5).optional(),
    maxHourlyRate: zod_1.z.number().min(0).optional(),
    minExperience: zod_1.z.number().min(0).optional(),
    languages: zod_1.z.string().optional(), // Comma-separated languages
    maxDistance: zod_1.z.number().min(0).optional(),
    lat: zod_1.z.number().optional(),
    lng: zod_1.z.number().optional(),
    isAvailable: zod_1.z.boolean().default(true),
    isVerified: zod_1.z.boolean().optional(),
    sortBy: zod_1.z.enum(['rating', 'experience', 'distance', 'hourly_rate', 'total_reviews']).default('rating'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20)
});
const globalSearchSchema = zod_1.z.object({
    q: zod_1.z.string().min(1, 'Search query is required'),
    type: zod_1.z.enum(['all', 'services', 'experts']).default('all'),
    governorate: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    lat: zod_1.z.number().optional(),
    lng: zod_1.z.number().optional(),
    maxDistance: zod_1.z.number().min(0).optional(),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20)
});
/**
 * @swagger
 * components:
 *   schemas:
 *     SearchResult:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [service, expert]
 *           description: Type of search result
 *         id:
 *           type: string
 *           description: Result ID
 *         title:
 *           type: string
 *           description: Result title
 *         description:
 *           type: string
 *           description: Result description
 *         category:
 *           type: string
 *           description: Category
 *         price:
 *           type: number
 *           description: Price (for services) or hourly rate (for experts)
 *         rating:
 *           type: number
 *           description: Average rating
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *         distance:
 *           type: number
 *           description: Distance from user location (if provided)
 *         expert:
 *           type: object
 *           description: Expert information (for services)
 *           properties:
 *             id:
 *               type: string
 *             name:
 *               type: string
 *             rating:
 *               type: number
 *         imageUrl:
 *           type: string
 *           description: Result image URL
 *
 *     SearchResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             results:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/SearchResult'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: number
 *                 limit:
 *                   type: number
 *                 total:
 *                   type: number
 *                 totalPages:
 *                   type: number
 *             filters:
 *               type: object
 *               description: Applied filters summary
 */
/**
 * @swagger
 * /api/v1/search/services:
 *   get:
 *     summary: Search services with advanced filters
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "تطوير موقع"
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Service category
 *         example: "تطوير الويب"
 *       - in: query
 *         name: serviceType
 *         schema:
 *           type: string
 *           enum: [DIGITAL, PHYSICAL]
 *         description: Type of service
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Governorate filter
 *         example: "دمشق"
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *         example: "دمشق"
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *         description: User latitude for distance calculation
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *         description: User longitude for distance calculation
 *       - in: query
 *         name: maxDistance
 *         schema:
 *           type: number
 *         description: Maximum distance in kilometers
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [price, rating, distance, created_at, popularity]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Services search results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SearchResponse'
 */
router.get('/services', auth_1.optionalAuthenticate, (0, validation_1.validateQuery)(searchServicesSchema), searchController.searchServices);
/**
 * @swagger
 * /api/v1/search/experts:
 *   get:
 *     summary: Search experts with advanced filters
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "مطور React"
 *       - in: query
 *         name: skills
 *         schema:
 *           type: string
 *         description: Comma-separated skills
 *         example: "React,Node.js,JavaScript"
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Governorate filter
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *       - in: query
 *         name: availability
 *         schema:
 *           type: string
 *           enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *         description: Availability filter
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *       - in: query
 *         name: maxHourlyRate
 *         schema:
 *           type: number
 *         description: Maximum hourly rate filter
 *       - in: query
 *         name: isVerified
 *         schema:
 *           type: boolean
 *         description: Filter by verification status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, experience, distance, hourly_rate, total_reviews]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Experts search results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SearchResponse'
 */
router.get('/experts', auth_1.optionalAuthenticate, (0, validation_1.validateQuery)(searchExpertsSchema), searchController.searchExperts);
/**
 * @swagger
 * /api/v1/search:
 *   get:
 *     summary: Global search across services and experts
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "تطوير تطبيقات"
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, services, experts]
 *         description: Search type filter
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Location filter
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *         description: User latitude for distance-based results
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *         description: User longitude for distance-based results
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Global search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: object
 *                       properties:
 *                         services:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/SearchResult'
 *                         experts:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/SearchResult'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         totalServices:
 *                           type: number
 *                         totalExperts:
 *                           type: number
 *                     query:
 *                       type: string
 *                       description: Original search query
 */
router.get('/', auth_1.optionalAuthenticate, (0, validation_1.validateQuery)(globalSearchSchema), searchController.globalSearch);
/**
 * @swagger
 * /api/v1/search/suggestions:
 *   get:
 *     summary: Get search suggestions and autocomplete
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: Partial search query
 *         example: "تطو"
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [services, experts, categories, skills]
 *         description: Type of suggestions
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 20
 *         description: Number of suggestions
 *     responses:
 *       200:
 *         description: Search suggestions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           text:
 *                             type: string
 *                           type:
 *                             type: string
 *                           count:
 *                             type: number
 */
router.get('/suggestions', (0, validation_1.validateQuery)(zod_1.z.object({
    q: zod_1.z.string().min(2, 'Query must be at least 2 characters'),
    type: zod_1.z.enum(['services', 'experts', 'categories', 'skills']).optional(),
    limit: zod_1.z.number().min(1).max(20).default(10)
})), searchController.getSearchSuggestions);
exports.default = router;
//# sourceMappingURL=search.js.map