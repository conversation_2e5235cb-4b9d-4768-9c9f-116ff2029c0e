import { Request, Response } from 'express';
import { dbService } from '@freela/database';
import { passwordUtils, sessionUtils, authUtils, jwtUtils, fallbackSessionHelpers } from '../utils/auth';
import { logUserAction, logSecurityEvent } from '../utils/logger';
import { createError } from '../utils/errors';
import { asyncHandler } from '../utils/asyncHandler';
import { redis, sessionHelpers } from '../utils/redis';
import { nanoid } from 'nanoid';

/**
 * User registration
 */
const register = asyncHandler(async (req: Request, res: Response) => {
  const {
    email,
    password,
    firstName,
    lastName,
    phone,
    role,
    language = 'ar',
    acceptTerms,
  } = req.body;

  // Check if terms are accepted
  if (!acceptTerms) {
    throw createError.badRequest('You must accept the terms and conditions', 'TERMS_NOT_ACCEPTED');
  }

  // Check if user already exists
  const existingUser = await dbService.findUserByEmailOrPhone(email, phone);

  if (existingUser) {
    if (existingUser.email === email.toLowerCase()) {
      throw createError.conflict('Email already registered', 'EMAIL_EXISTS');
    }
    if (existingUser.phone === phone) {
      throw createError.conflict('Phone number already registered', 'PHONE_EXISTS');
    }
  }

  // Hash password
  const passwordHash = await passwordUtils.hash(password);

  // Generate verification token
  const emailVerificationToken = authUtils.generateSecureToken();

  // Create user
  const user = await dbService.createUser({
    email: email.toLowerCase(),
    firstName,
    lastName,
    phone,
    role: role.toUpperCase() as 'CLIENT' | 'EXPERT' | 'ADMIN',
    language,
    passwordHash,
    emailVerificationToken,
    status: 'PENDING_VERIFICATION',
  });

  // Store verification token in Redis with 24-hour expiry
  await redis.set(
    `email_verification:${emailVerificationToken}`,
    user.id,
    24 * 60 * 60 // 24 hours
  );

  // Log user registration
  logUserAction('user_registered', user.id, {
    email: user.email,
    role: user.role,
    language: user.language,
  });

  // TODO: Send verification email
  // await emailService.sendVerificationEmail(user.email, emailVerificationToken);

  res.status(201).json({
    success: true,
    message: 'Registration successful. Please check your email for verification.',
    data: {
      user,
      verificationRequired: true,
    },
  });
});

/**
 * User login
 */
const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password, rememberMe = false } = req.body;

  // Find user by email
  const user = await dbService.findUserByEmail(email, {
    id: true,
    email: true,
    firstName: true,
    lastName: true,
    role: true,
    status: true,
    emailVerified: true,
    phoneVerified: true,
    passwordHash: true,
    lastLoginAt: true,
  });

  if (!user) {
    logSecurityEvent('login_attempt_invalid_email', { email }, req);
    throw createError.unauthorized('Invalid email or password', 'INVALID_CREDENTIALS');
  }

  // Verify password
  const isPasswordValid = await passwordUtils.verify(password, user.passwordHash);
  if (!isPasswordValid) {
    logSecurityEvent('login_attempt_invalid_password', { email, userId: user.id }, req);
    throw createError.unauthorized('Invalid email or password', 'INVALID_CREDENTIALS');
  }

  // Check user status
  if (user.status === 'SUSPENDED') {
    logSecurityEvent('login_attempt_suspended_user', { userId: user.id }, req);
    throw createError.forbidden('Account is suspended', 'ACCOUNT_SUSPENDED');
  }

  if (user.status === 'INACTIVE') {
    logSecurityEvent('login_attempt_inactive_user', { userId: user.id }, req);
    throw createError.forbidden('Account is inactive', 'ACCOUNT_INACTIVE');
  }

  // Create session and generate tokens
  const tokens = await sessionUtils.createSession(
    user.id,
    user.email,
    user.role,
    {
      firstName: user.firstName,
      lastName: user.lastName,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      rememberMe,
    }
  );

  // Update last login time
  await dbService.updateUser(user.id, {
    lastLoginAt: new Date()
  });

  // Log successful login
  logUserAction('user_logged_in', user.id, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    rememberMe,
  });

  // Remove password hash from response
  const { passwordHash, ...userWithoutPassword } = user;

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: userWithoutPassword,
      tokens,
    },
  });
});

/**
 * Refresh access token
 */
const refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw createError.badRequest('Refresh token is required', 'REFRESH_TOKEN_REQUIRED');
  }

  const tokens = await sessionUtils.refreshTokens(refreshToken);
  if (!tokens) {
    logSecurityEvent('invalid_refresh_token', { token: refreshToken.substring(0, 20) + '...' }, req);
    throw createError.unauthorized('Invalid or expired refresh token', 'INVALID_REFRESH_TOKEN');
  }

  res.json({
    success: true,
    message: 'Tokens refreshed successfully',
    data: { tokens },
  });
});

/**
 * User logout
 */
const logout = asyncHandler(async (req: any, res: Response) => { // Temporarily use req: any
  if (!req.user) {
    throw createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
  }

  // Invalidate session
  await sessionUtils.invalidateSession(req.user.sessionId);

  // Log logout
  logUserAction('user_logged_out', req.user.id, {
    sessionId: req.user.sessionId,
  });

  res.json({
    success: true,
    message: 'Logout successful',
  });
});

/**
 * Logout from all devices
 */
const logoutAll = asyncHandler(async (req: any, res: Response) => { // Temporarily use req: any
  if (!req.user) {
    throw createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
  }

  // Invalidate all user sessions
  await sessionUtils.invalidateAllUserSessions(req.user.id);

  // Log logout from all devices
  logUserAction('user_logged_out_all', req.user.id);

  res.json({
    success: true,
    message: 'Logged out from all devices successfully',
  });
});

/**
 * Verify email
 */
const verifyEmail = asyncHandler(async (req: Request, res: Response) => {
  const { token } = req.body;

  if (!token) {
    throw createError.badRequest('Verification token is required', 'TOKEN_REQUIRED');
  }

  // Get user ID from Redis
  const userId = await redis.get(`email_verification:${token}`);
  if (!userId) {
    throw createError.badRequest('Invalid or expired verification token', 'INVALID_TOKEN');
  }

  // Update user email verification status
  const user = await dbService.updateUser(userId, {
    emailVerified: true,
    status: 'ACTIVE',
  });

  // Remove verification token from Redis
  await redis.del(`email_verification:${token}`);

  // Log email verification
  logUserAction('email_verified', user.id);

  res.json({
    success: true,
    message: 'Email verified successfully',
    data: { user },
  });
});

/**
 * Resend email verification
 */
const resendEmailVerification = asyncHandler(async (req: Request, res: Response) => {
  const { email } = req.body;

  const user = await dbService.findUserByEmail(email, {
    id: true,
    email: true,
    emailVerified: true,
    status: true,
  });

  if (!user) {
    throw createError.notFound('User not found', 'USER_NOT_FOUND');
  }

  if (user.emailVerified) {
    throw createError.badRequest('Email is already verified', 'EMAIL_ALREADY_VERIFIED');
  }

  // Generate new verification token
  const emailVerificationToken = authUtils.generateSecureToken();

  // Update user with new token
  await dbService.updateUser(user.id, {
    emailVerificationToken
  });

  // Store verification token in Redis
  await redis.set(
    `email_verification:${emailVerificationToken}`,
    user.id,
    24 * 60 * 60 // 24 hours
  );

  // TODO: Send verification email
  // await emailService.sendVerificationEmail(user.email, emailVerificationToken);

  // Log resend verification
  logUserAction('email_verification_resent', user.id);

  res.json({
    success: true,
    message: 'Verification email sent successfully',
  });
});

/**
 * Request password reset
 */
const requestPasswordReset = asyncHandler(async (req: Request, res: Response) => {
  const { email } = req.body;

  const user = await dbService.findUserByEmail(email, {
    id: true,
    email: true,
    firstName: true,
  });

  // Always return success to prevent email enumeration
  if (!user) {
    return res.json({
      success: true,
      message: 'If the email exists, a password reset link has been sent',
    });
  }

  // Generate reset token
  const resetToken = authUtils.generateSecureToken();

  // Store reset token in Redis with 1-hour expiry
  await redis.set(
    `password_reset:${resetToken}`,
    user.id,
    60 * 60 // 1 hour
  );

  // TODO: Send password reset email
  // await emailService.sendPasswordResetEmail(user.email, resetToken);

  // Log password reset request
  logUserAction('password_reset_requested', user.id);

  res.json({
    success: true,
    message: 'If the email exists, a password reset link has been sent',
  });
});

/**
 * Reset password
 */
const resetPassword = asyncHandler(async (req: Request, res: Response) => {
  const { token, newPassword } = req.body;

  if (!token || !newPassword) {
    throw createError.badRequest('Token and new password are required', 'MISSING_FIELDS');
  }

  // Validate password strength
  const passwordValidation = authUtils.validatePasswordStrength(newPassword);
  if (!passwordValidation.isValid) {
    throw createError.badRequest('Password does not meet requirements', 'WEAK_PASSWORD', {
      errors: passwordValidation.errors,
    });
  }

  // Get user ID from Redis
  const userId = await redis.get(`password_reset:${token}`);
  if (!userId) {
    throw createError.badRequest('Invalid or expired reset token', 'INVALID_TOKEN');
  }

  // Hash new password
  const passwordHash = await passwordUtils.hash(newPassword);

  // Update user password
  await dbService.updateUser(userId, {
    passwordHash
  });

  // Remove reset token from Redis
  await redis.del(`password_reset:${token}`);

  // Invalidate all user sessions
  await sessionUtils.invalidateAllUserSessions(userId);

  // Log password reset
  logUserAction('password_reset_completed', userId);

  res.json({
    success: true,
    message: 'Password reset successfully',
  });
});

/**
 * Get current user profile
 */
const getProfile = asyncHandler(async (req: any, res: Response) => { // Temporarily use req: any
  if (!req.user) {
    throw createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
  }

  const user = await dbService.findUserById(req.user.id, {
    id: true,
    email: true,
    firstName: true,
    lastName: true,
    phone: true,
    role: true,
    status: true,
    language: true,
    emailVerified: true,
    phoneVerified: true,
    lastLoginAt: true,
    createdAt: true,
  });

  if (!user) {
    throw createError.notFound('User not found', 'USER_NOT_FOUND');
  }

  res.json({
    success: true,
    data: { user },
  });
});

/**
 * NextAuth integration login
 */
const nextAuthLogin = asyncHandler(async (req: Request, res: Response) => {
  const { email, name, image, provider = 'google' } = req.body;

  try {
    // For development/testing without database, create a mock user
    const [firstName, ...lastNameParts] = (name || '').split(' ');
    const lastName = lastNameParts.join(' ');

    // Generate a mock user ID based on email
    const userId = `user_${Buffer.from(email).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)}`;

    const mockUser = {
      id: userId,
      email: email.toLowerCase(),
      firstName: firstName || 'User',
      lastName: lastName || '',
      profilePicture: image,
      role: 'CLIENT' as const,
      status: 'ACTIVE' as const,
      emailVerified: true,
      phoneVerified: false,
      createdAt: new Date(),
      lastLoginAt: new Date(),
    };

    // Generate session and tokens manually (without database)
    const sessionId = nanoid();
    const tokens = {
      accessToken: jwtUtils.generateAccessToken({
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        sessionId,
      }),
      refreshToken: jwtUtils.generateRefreshToken({
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        sessionId,
      }),
      expiresIn: 15 * 60, // 15 minutes
      refreshExpiresIn: 7 * 24 * 60 * 60, // 7 days
    };

    // Store session using Redis or fallback to in-memory store
    try {
      const sessionData = {
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        userData: {
          provider,
          loginTime: new Date().toISOString(),
          userAgent: req.headers['user-agent'] || '',
          ip: req.ip || '',
        },
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
      };

      // Try Redis first, fallback to in-memory store
      try {
        await sessionHelpers.setSession(sessionId, mockUser.id, sessionData, 7 * 24 * 60 * 60); // 7 days
        await redis.set(`refresh:${sessionId}`, tokens.refreshToken, 7 * 24 * 60 * 60);
      } catch (redisError) {
        console.warn('Redis unavailable, using in-memory session store:', redisError);
        // Use in-memory fallback
        await fallbackSessionHelpers.setSession(sessionId, mockUser.id, sessionData, 7 * 24 * 60 * 60);
      }
    } catch (sessionError) {
      console.warn('Session storage failed:', sessionError);
    }

    logUserAction('nextauth_login_successful', mockUser.id, {
      email: mockUser.email,
      isNewUser: true, // Always new for mock
      provider,
    });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          profilePicture: mockUser.profilePicture,
          role: mockUser.role,
          status: mockUser.status,
        },
        tokens,
        isNewUser: true,
      },
    });
  } catch (error) {
    logSecurityEvent('nextauth_login_failed', { error: (error as Error).message, email }, req);
    throw createError.internalServerError('Login failed');
  }
});

export {
  register,
  login,
  refreshToken,
  logout,
  logoutAll,
  verifyEmail,
  resendEmailVerification,
  requestPasswordReset,
  resetPassword,
  getProfile,
  nextAuthLogin,
};
