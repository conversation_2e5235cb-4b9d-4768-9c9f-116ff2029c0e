"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const servicesController = __importStar(require("../controllers/services"));
const zod_1 = require("zod");
const router = (0, express_1.Router)();
// Validation schemas for services
const createServiceSchema = zod_1.z.object({
    title: zod_1.z.string().min(3, 'Service title must be at least 3 characters').max(100, 'Service title must be less than 100 characters'),
    description: zod_1.z.string().min(10, 'Service description must be at least 10 characters').max(2000, 'Service description must be less than 2000 characters'),
    category: zod_1.z.string().min(1, 'Category is required'),
    subcategory: zod_1.z.string().optional(),
    tags: zod_1.z.array(zod_1.z.string()).max(10, 'Maximum 10 tags allowed').optional(),
    priceType: zod_1.z.enum(['FIXED', 'HOURLY', 'NEGOTIABLE']).default('FIXED'),
    basePrice: zod_1.z.number().min(0, 'Price must be positive'),
    currency: zod_1.z.string().default('USD'),
    duration: zod_1.z.number().min(1, 'Duration must be at least 1 minute').optional(),
    deliveryTime: zod_1.z.number().min(1, 'Delivery time must be at least 1 day').optional(),
    revisions: zod_1.z.number().min(0, 'Revisions cannot be negative').optional(),
    requirements: zod_1.z.array(zod_1.z.string()).optional(),
    portfolio: zod_1.z.array(zod_1.z.object({
        title: zod_1.z.string(),
        description: zod_1.z.string().optional(),
        imageUrl: zod_1.z.string().url().optional(),
        projectUrl: zod_1.z.string().url().optional()
    })).optional(),
    serviceType: zod_1.z.enum(['DIGITAL', 'PHYSICAL']).default('DIGITAL'),
    location: zod_1.z.object({
        governorate: zod_1.z.string().optional(),
        city: zod_1.z.string().optional(),
        address: zod_1.z.string().optional(),
        coordinates: zod_1.z.object({
            lat: zod_1.z.number(),
            lng: zod_1.z.number()
        }).optional()
    }).optional(),
    isActive: zod_1.z.boolean().default(true)
});
const updateServiceSchema = createServiceSchema.partial();
const searchServicesSchema = zod_1.z.object({
    q: zod_1.z.string().optional(), // Search query
    category: zod_1.z.string().optional(),
    subcategory: zod_1.z.string().optional(),
    serviceType: zod_1.z.enum(['DIGITAL', 'PHYSICAL']).optional(),
    priceType: zod_1.z.enum(['FIXED', 'HOURLY', 'NEGOTIABLE']).optional(),
    minPrice: zod_1.z.number().min(0).optional(),
    maxPrice: zod_1.z.number().min(0).optional(),
    governorate: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    tags: zod_1.z.string().optional(), // Comma-separated tags
    sortBy: zod_1.z.enum(['price', 'rating', 'created_at', 'popularity']).default('created_at'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20),
    expertId: zod_1.z.string().optional(),
    isActive: zod_1.z.boolean().optional()
});
/**
 * @swagger
 * components:
 *   schemas:
 *     Service:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Service ID
 *         expertId:
 *           type: string
 *           description: Expert ID who owns the service
 *         title:
 *           type: string
 *           description: Service title
 *         description:
 *           type: string
 *           description: Service description
 *         category:
 *           type: string
 *           description: Service category
 *         subcategory:
 *           type: string
 *           description: Service subcategory
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Service tags for search
 *         priceType:
 *           type: string
 *           enum: [FIXED, HOURLY, NEGOTIABLE]
 *           description: Pricing type
 *         basePrice:
 *           type: number
 *           description: Base price for the service
 *         currency:
 *           type: string
 *           description: Currency code
 *         duration:
 *           type: number
 *           description: Service duration in minutes
 *         deliveryTime:
 *           type: number
 *           description: Delivery time in days
 *         revisions:
 *           type: number
 *           description: Number of revisions included
 *         serviceType:
 *           type: string
 *           enum: [DIGITAL, PHYSICAL]
 *           description: Type of service
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *             coordinates:
 *               type: object
 *               properties:
 *                 lat:
 *                   type: number
 *                 lng:
 *                   type: number
 *         isActive:
 *           type: boolean
 *           description: Service availability status
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
/**
 * @swagger
 * /api/v1/services:
 *   post:
 *     summary: Create a new service
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - category
 *               - basePrice
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 100
 *                 example: "تطوير موقع إلكتروني متجاوب"
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *                 example: "أقدم خدمة تطوير مواقع إلكترونية متجاوبة باستخدام أحدث التقنيات"
 *               category:
 *                 type: string
 *                 example: "تطوير الويب"
 *               subcategory:
 *                 type: string
 *                 example: "مواقع الشركات"
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["React", "Node.js", "تصميم متجاوب"]
 *               priceType:
 *                 type: string
 *                 enum: [FIXED, HOURLY, NEGOTIABLE]
 *                 example: "FIXED"
 *               basePrice:
 *                 type: number
 *                 minimum: 0
 *                 example: 500
 *               currency:
 *                 type: string
 *                 example: "USD"
 *               serviceType:
 *                 type: string
 *                 enum: [DIGITAL, PHYSICAL]
 *                 example: "DIGITAL"
 *               location:
 *                 type: object
 *                 properties:
 *                   governorate:
 *                     type: string
 *                     example: "دمشق"
 *                   city:
 *                     type: string
 *                     example: "دمشق"
 *     responses:
 *       201:
 *         description: Service created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Service created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Expert role required
 */
router.post('/', auth_1.authenticate, auth_1.expertOnly, (0, validation_1.validateBody)(createServiceSchema), servicesController.createService);
/**
 * @swagger
 * /api/v1/services:
 *   get:
 *     summary: Search and list services
 *     tags: [Services]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: serviceType
 *         schema:
 *           type: string
 *           enum: [DIGITAL, PHYSICAL]
 *         description: Filter by service type
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Filter by governorate (for physical services)
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Filter by city (for physical services)
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [price, rating, created_at, popularity]
 *         description: Sort by field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Services retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     services:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Service'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         total:
 *                           type: number
 *                         totalPages:
 *                           type: number
 */
router.get('/', (0, validation_1.validateQuery)(searchServicesSchema), servicesController.searchServices);
/**
 * @swagger
 * /api/v1/services/{id}:
 *   get:
 *     summary: Get service by ID
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Service retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *       404:
 *         description: Service not found
 */
router.get('/:id', servicesController.getServiceById);
/**
 * @swagger
 * /api/v1/services/{id}:
 *   put:
 *     summary: Update service
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 100
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *               category:
 *                 type: string
 *               basePrice:
 *                 type: number
 *                 minimum: 0
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Service updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Service updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - not service owner
 *       404:
 *         description: Service not found
 */
router.put('/:id', auth_1.authenticate, auth_1.expertOrAdmin, (0, validation_1.validateBody)(updateServiceSchema), servicesController.updateService);
/**
 * @swagger
 * /api/v1/services/{id}:
 *   delete:
 *     summary: Delete service
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Service deleted successfully"
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - not service owner
 *       404:
 *         description: Service not found
 */
router.delete('/:id', auth_1.authenticate, auth_1.expertOrAdmin, servicesController.deleteService);
/**
 * @swagger
 * /api/v1/services/expert/{expertId}:
 *   get:
 *     summary: Get services by expert ID
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: expertId
 *         required: true
 *         schema:
 *           type: string
 *         description: Expert ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Expert services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Expert services retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     services:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Service'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         total:
 *                           type: number
 *                         totalPages:
 *                           type: number
 */
router.get('/expert/:expertId', (0, validation_1.validateQuery)(searchServicesSchema), servicesController.getServicesByExpert);
exports.default = router;
//# sourceMappingURL=services.js.map