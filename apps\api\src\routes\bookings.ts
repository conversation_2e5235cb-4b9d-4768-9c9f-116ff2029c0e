import { Router } from 'express';
import { validateBody, validateQuery } from '../middleware/validation';
import { authenticate, clientOnly, expertOnly, expertOrAdmin, anyUser, adminOnly } from '../middleware/auth';
import * as bookingsController from '../controllers/bookings';
import { z } from 'zod';

const router = Router();

// Validation schemas for bookings
const createBookingSchema = z.object({
  serviceId: z.string().min(1, 'Service ID is required'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(1000, 'Message must be less than 1000 characters'),
  requirements: z.array(z.string()).optional(),
  budget: z.number().min(0, 'Budget must be positive').optional(),
  deadline: z.string().datetime().optional(),
  location: z.object({
    governorate: z.string().optional(),
    city: z.string().optional(),
    address: z.string().optional(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number()
    }).optional()
  }).optional(),
  contactPreference: z.enum(['CHAT', 'PHONE', 'EMAIL']).default('CHAT'),
  urgency: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM')
});

const updateBookingStatusSchema = z.object({
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']),
  message: z.string().optional(),
  estimatedDelivery: z.string().datetime().optional(),
  finalPrice: z.number().min(0).optional()
});

const searchBookingsSchema = z.object({
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
  serviceId: z.string().optional(),
  expertId: z.string().optional(),
  clientId: z.string().optional(),
  urgency: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  fromDate: z.string().datetime().optional(),
  toDate: z.string().datetime().optional(),
  sortBy: z.enum(['created_at', 'deadline', 'budget', 'status']).default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20)
});

/**
 * @swagger
 * components:
 *   schemas:
 *     Booking:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Booking ID
 *         serviceId:
 *           type: string
 *           description: Service ID
 *         clientId:
 *           type: string
 *           description: Client ID
 *         expertId:
 *           type: string
 *           description: Expert ID
 *         status:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *           description: Booking status
 *         message:
 *           type: string
 *           description: Initial message from client
 *         requirements:
 *           type: array
 *           items:
 *             type: string
 *           description: Project requirements
 *         budget:
 *           type: number
 *           description: Client's budget
 *         finalPrice:
 *           type: number
 *           description: Final agreed price
 *         deadline:
 *           type: string
 *           format: date-time
 *           description: Project deadline
 *         estimatedDelivery:
 *           type: string
 *           format: date-time
 *           description: Expert's estimated delivery date
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *             coordinates:
 *               type: object
 *               properties:
 *                 lat:
 *                   type: number
 *                 lng:
 *                   type: number
 *         contactPreference:
 *           type: string
 *           enum: [CHAT, PHONE, EMAIL]
 *           description: Preferred contact method
 *         urgency:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *           description: Project urgency level
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     
 *     BookingResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           $ref: '#/components/schemas/Booking'
 *     
 *     BookingsListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             bookings:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Booking'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: number
 *                 limit:
 *                   type: number
 *                 total:
 *                   type: number
 *                 totalPages:
 *                   type: number
 */

/**
 * @swagger
 * /api/v1/bookings:
 *   post:
 *     summary: Create a new booking request
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceId
 *               - message
 *             properties:
 *               serviceId:
 *                 type: string
 *                 example: "service_123"
 *               message:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 1000
 *                 example: "أحتاج إلى تطوير موقع إلكتروني لشركتي الناشئة"
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["تصميم متجاوب", "لوحة إدارة", "نظام دفع"]
 *               budget:
 *                 type: number
 *                 minimum: 0
 *                 example: 1000
 *               deadline:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-02-15T00:00:00Z"
 *               urgency:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *                 example: "MEDIUM"
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Client role required
 */
router.post('/',
  authenticate,
  clientOnly,
  validateBody(createBookingSchema),
  bookingsController.createBooking
);

/**
 * @swagger
 * /api/v1/bookings:
 *   get:
 *     summary: Get bookings with filters
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: serviceId
 *         schema:
 *           type: string
 *         description: Filter by service ID
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *         description: Filter by urgency level
 *       - in: query
 *         name: fromDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter bookings from this date
 *       - in: query
 *         name: toDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter bookings to this date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created_at, deadline, budget, status]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/',
  authenticate,
  anyUser,
  validateQuery(searchBookingsSchema),
  bookingsController.getBookings
);

/**
 * @swagger
 * /api/v1/bookings/{id}:
 *   get:
 *     summary: Get booking by ID
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     responses:
 *       200:
 *         description: Booking retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied
 *       404:
 *         description: Booking not found
 */
router.get('/:id',
  authenticate,
  anyUser,
  bookingsController.getBookingById
);

/**
 * @swagger
 * /api/v1/bookings/{id}/status:
 *   put:
 *     summary: Update booking status
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *                 example: "ACCEPTED"
 *               message:
 *                 type: string
 *                 example: "أوافق على المشروع وسأبدأ العمل فوراً"
 *               estimatedDelivery:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-02-10T00:00:00Z"
 *               finalPrice:
 *                 type: number
 *                 minimum: 0
 *                 example: 800
 *     responses:
 *       200:
 *         description: Booking status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       400:
 *         description: Validation error or invalid status transition
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied
 *       404:
 *         description: Booking not found
 */
router.put('/:id/status',
  authenticate,
  anyUser,
  validateBody(updateBookingStatusSchema),
  bookingsController.updateBookingStatus
);

/**
 * @swagger
 * /api/v1/bookings/my/client:
 *   get:
 *     summary: Get current user's bookings as client
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Client bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/my/client',
  authenticate,
  validateQuery(searchBookingsSchema),
  bookingsController.getMyClientBookings
);

/**
 * @swagger
 * /api/v1/bookings/my/expert:
 *   get:
 *     summary: Get current user's bookings as expert
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Expert bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/my/expert',
  authenticate,
  validateQuery(searchBookingsSchema),
  bookingsController.getMyExpertBookings
);

export default router;
