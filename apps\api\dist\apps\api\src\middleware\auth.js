"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.anyUser = exports.clientOrAdmin = exports.expertOrAdmin = exports.clientOnly = exports.expertOnly = exports.adminOnly = exports.requireOwnership = exports.requirePhoneVerification = exports.requireEmailVerification = exports.authorize = exports.optionalAuthenticate = exports.authenticate = void 0;
const auth_1 = require("../utils/auth");
const logger_1 = require("../utils/logger");
const database_1 = require("@freela/database");
/**
 * Authentication middleware - verifies JWT token and loads user data
 */
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const userIdHeader = req.headers['x-user-id'];
        const token = auth_1.authUtils.extractTokenFromHeader(authHeader);
        // For development/testing: allow direct user ID authentication from landing page
        if (!token && userIdHeader) {
            try {
                // Load user data directly by ID (for landing page integration)
                const user = await database_1.dbService.findUserById(userIdHeader, {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    status: true,
                    emailVerified: true,
                    phoneVerified: true,
                });
                if (user && user.status === 'ACTIVE') {
                    req.user = {
                        ...user,
                        sessionId: `temp_session_${userIdHeader}`,
                    };
                    return next();
                }
            }
            catch (error) {
                logger_1.logger.warn('Direct user ID authentication failed', { error, userIdHeader });
            }
        }
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access token is required',
                code: 'TOKEN_MISSING',
            });
        }
        // Verify token
        const payload = auth_1.jwtUtils.verifyAccessToken(token);
        if (!payload) {
            (0, logger_1.logSecurityEvent)('invalid_token_attempt', { token: token.substring(0, 20) + '...' }, req);
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired access token',
                code: 'TOKEN_INVALID',
            });
        }
        // Validate session
        const isSessionValid = await auth_1.sessionUtils.validateSession(payload.sessionId);
        if (!isSessionValid) {
            (0, logger_1.logSecurityEvent)('invalid_session_attempt', { sessionId: payload.sessionId, userId: payload.userId }, req);
            return res.status(401).json({
                success: false,
                message: 'Session has expired',
                code: 'SESSION_EXPIRED',
            });
        }
        // Load user data from database or create mock user for development
        let user;
        try {
            user = await database_1.dbService.findUserById(payload.userId, {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                status: true,
                emailVerified: true,
                phoneVerified: true,
            });
        }
        catch (dbError) {
            logger_1.logger.warn('Database unavailable, using mock user for development', { userId: payload.userId });
            // Create mock user for development when database is unavailable
            if (payload.userId && payload.email && payload.role) {
                user = {
                    id: payload.userId,
                    email: payload.email,
                    firstName: 'Mock',
                    lastName: 'User',
                    role: payload.role,
                    status: 'ACTIVE',
                    emailVerified: true,
                    phoneVerified: false,
                };
            }
        }
        if (!user) {
            (0, logger_1.logSecurityEvent)('user_not_found', { userId: payload.userId }, req);
            return res.status(401).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND',
            });
        }
        // Check if user is active
        if (user.status !== 'ACTIVE') {
            (0, logger_1.logSecurityEvent)('inactive_user_attempt', { userId: user.id, status: user.status }, req);
            return res.status(403).json({
                success: false,
                message: 'Account is not active',
                code: 'ACCOUNT_INACTIVE',
            });
        }
        // Update session activity
        await auth_1.sessionUtils.updateSessionActivity(payload.sessionId);
        // Attach user to request
        req.user = {
            ...user,
            sessionId: payload.sessionId,
        };
        next();
    }
    catch (error) {
        logger_1.logger.error('Authentication middleware error', { error });
        return res.status(500).json({
            success: false,
            message: 'Authentication failed',
            code: 'AUTH_ERROR',
        });
    }
};
exports.authenticate = authenticate;
/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
const optionalAuthenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = auth_1.authUtils.extractTokenFromHeader(authHeader);
        if (!token) {
            return next();
        }
        // Try to authenticate, but don't fail if it doesn't work
        const payload = auth_1.jwtUtils.verifyAccessToken(token);
        if (payload) {
            const isSessionValid = await auth_1.sessionUtils.validateSession(payload.sessionId);
            if (isSessionValid) {
                const user = await database_1.dbService.findUserById(payload.userId, {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    status: true,
                    emailVerified: true,
                    phoneVerified: true,
                });
                if (user && user.status === 'ACTIVE') {
                    req.user = {
                        ...user,
                        sessionId: payload.sessionId,
                    };
                    await auth_1.sessionUtils.updateSessionActivity(payload.sessionId);
                }
            }
        }
        next();
    }
    catch (error) {
        logger_1.logger.error('Optional authentication middleware error', { error });
        next(); // Continue without authentication
    }
};
exports.optionalAuthenticate = optionalAuthenticate;
/**
 * Role-based authorization middleware
 */
const authorize = (...allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                code: 'AUTH_REQUIRED',
            });
        }
        if (!allowedRoles.includes(req.user.role)) {
            (0, logger_1.logSecurityEvent)('unauthorized_access_attempt', {
                userId: req.user.id,
                userRole: req.user.role,
                requiredRoles: allowedRoles,
                endpoint: req.path,
            }, req);
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions',
                code: 'INSUFFICIENT_PERMISSIONS',
            });
        }
        next();
    };
};
exports.authorize = authorize;
/**
 * Email verification requirement middleware
 */
const requireEmailVerification = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required',
            code: 'AUTH_REQUIRED',
        });
    }
    if (!req.user.emailVerified) {
        return res.status(403).json({
            success: false,
            message: 'Email verification required',
            code: 'EMAIL_VERIFICATION_REQUIRED',
        });
    }
    next();
};
exports.requireEmailVerification = requireEmailVerification;
/**
 * Phone verification requirement middleware
 */
const requirePhoneVerification = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required',
            code: 'AUTH_REQUIRED',
        });
    }
    if (!req.user.phoneVerified) {
        return res.status(403).json({
            success: false,
            message: 'Phone verification required',
            code: 'PHONE_VERIFICATION_REQUIRED',
        });
    }
    next();
};
exports.requirePhoneVerification = requirePhoneVerification;
/**
 * Resource ownership middleware - checks if user owns the resource
 */
const requireOwnership = (resourceIdParam = 'id', userIdField = 'userId') => {
    return async (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                code: 'AUTH_REQUIRED',
            });
        }
        const resourceId = req.params[resourceIdParam];
        if (!resourceId) {
            return res.status(400).json({
                success: false,
                message: 'Resource ID is required',
                code: 'RESOURCE_ID_REQUIRED',
            });
        }
        try {
            // This is a generic implementation - you might need to customize based on your models
            // For now, we'll check if the user ID matches
            if (userIdField === 'userId' && resourceId !== req.user.id) {
                (0, logger_1.logSecurityEvent)('unauthorized_resource_access', {
                    userId: req.user.id,
                    resourceId,
                    resourceType: req.route?.path || req.path,
                }, req);
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this resource',
                    code: 'ACCESS_DENIED',
                });
            }
            next();
        }
        catch (error) {
            logger_1.logger.error('Ownership check error', { error, resourceId, userId: req.user.id });
            return res.status(500).json({
                success: false,
                message: 'Failed to verify resource ownership',
                code: 'OWNERSHIP_CHECK_ERROR',
            });
        }
    };
};
exports.requireOwnership = requireOwnership;
/**
 * Admin only middleware
 */
exports.adminOnly = (0, exports.authorize)('ADMIN');
/**
 * Expert only middleware
 */
exports.expertOnly = (0, exports.authorize)('EXPERT');
/**
 * Client only middleware
 */
exports.clientOnly = (0, exports.authorize)('CLIENT');
/**
 * Expert or Admin middleware
 */
exports.expertOrAdmin = (0, exports.authorize)('EXPERT', 'ADMIN');
/**
 * Client or Admin middleware
 */
exports.clientOrAdmin = (0, exports.authorize)('CLIENT', 'ADMIN');
/**
 * Any authenticated user middleware
 */
exports.anyUser = (0, exports.authorize)('CLIENT', 'EXPERT', 'ADMIN');
//# sourceMappingURL=auth.js.map