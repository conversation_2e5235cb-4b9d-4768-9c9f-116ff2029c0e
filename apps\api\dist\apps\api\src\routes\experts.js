"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const expertsController = __importStar(require("../controllers/experts"));
const zod_1 = require("zod");
const router = (0, express_1.Router)();
// Validation schemas for expert profiles
const createExpertProfileSchema = zod_1.z.object({
    bio: zod_1.z.string().min(50, 'Bio must be at least 50 characters').max(1000, 'Bio must be less than 1000 characters'),
    skills: zod_1.z.array(zod_1.z.string()).min(1, 'At least one skill is required').max(20, 'Maximum 20 skills allowed'),
    experience: zod_1.z.number().min(0, 'Experience cannot be negative').max(50, 'Experience cannot exceed 50 years'),
    hourlyRate: zod_1.z.number().min(0, 'Hourly rate must be positive').optional(),
    availability: zod_1.z.enum(['FULL_TIME', 'PART_TIME', 'WEEKENDS', 'FLEXIBLE']).default('FLEXIBLE'),
    languages: zod_1.z.array(zod_1.z.string()).default(['ar']),
    education: zod_1.z.array(zod_1.z.object({
        degree: zod_1.z.string(),
        institution: zod_1.z.string(),
        year: zod_1.z.number().min(1950).max(new Date().getFullYear()),
        field: zod_1.z.string().optional()
    })).optional(),
    certifications: zod_1.z.array(zod_1.z.object({
        name: zod_1.z.string(),
        issuer: zod_1.z.string(),
        year: zod_1.z.number().min(1950).max(new Date().getFullYear()),
        url: zod_1.z.string().url().optional()
    })).optional(),
    portfolio: zod_1.z.array(zod_1.z.object({
        title: zod_1.z.string(),
        description: zod_1.z.string(),
        imageUrl: zod_1.z.string().url().optional(),
        projectUrl: zod_1.z.string().url().optional(),
        technologies: zod_1.z.array(zod_1.z.string()).optional(),
        completedAt: zod_1.z.string().optional()
    })).optional(),
    location: zod_1.z.object({
        governorate: zod_1.z.string(),
        city: zod_1.z.string(),
        address: zod_1.z.string().optional(),
        coordinates: zod_1.z.object({
            lat: zod_1.z.number(),
            lng: zod_1.z.number()
        }).optional()
    }),
    serviceAreas: zod_1.z.array(zod_1.z.object({
        governorate: zod_1.z.string(),
        cities: zod_1.z.array(zod_1.z.string()),
        travelCost: zod_1.z.number().min(0).optional(),
        maxDistance: zod_1.z.number().min(0).optional()
    })).optional(),
    socialLinks: zod_1.z.object({
        website: zod_1.z.string().url().optional(),
        linkedin: zod_1.z.string().url().optional(),
        github: zod_1.z.string().url().optional(),
        behance: zod_1.z.string().url().optional(),
        dribbble: zod_1.z.string().url().optional()
    }).optional(),
    isAvailable: zod_1.z.boolean().default(true),
    isVerified: zod_1.z.boolean().default(false)
});
const updateExpertProfileSchema = createExpertProfileSchema.partial();
const searchExpertsSchema = zod_1.z.object({
    q: zod_1.z.string().optional(), // Search query
    skills: zod_1.z.string().optional(), // Comma-separated skills
    category: zod_1.z.string().optional(),
    governorate: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    availability: zod_1.z.enum(['FULL_TIME', 'PART_TIME', 'WEEKENDS', 'FLEXIBLE']).optional(),
    minRating: zod_1.z.number().min(0).max(5).optional(),
    maxHourlyRate: zod_1.z.number().min(0).optional(),
    minExperience: zod_1.z.number().min(0).optional(),
    languages: zod_1.z.string().optional(), // Comma-separated languages
    isAvailable: zod_1.z.boolean().optional(),
    isVerified: zod_1.z.boolean().optional(),
    sortBy: zod_1.z.enum(['rating', 'experience', 'hourly_rate', 'total_reviews', 'created_at']).default('rating'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20)
});
/**
 * @swagger
 * components:
 *   schemas:
 *     ExpertProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Expert profile ID
 *         userId:
 *           type: string
 *           description: User ID associated with expert profile
 *         bio:
 *           type: string
 *           description: Expert biography
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           description: Expert skills
 *         experience:
 *           type: number
 *           description: Years of experience
 *         hourlyRate:
 *           type: number
 *           description: Hourly rate in USD
 *         availability:
 *           type: string
 *           enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *           description: Availability status
 *         languages:
 *           type: array
 *           items:
 *             type: string
 *           description: Languages spoken
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *             coordinates:
 *               type: object
 *               properties:
 *                 lat:
 *                   type: number
 *                 lng:
 *                   type: number
 *         rating:
 *           type: number
 *           description: Average rating (0-5)
 *         totalReviews:
 *           type: number
 *           description: Total number of reviews
 *         completedProjects:
 *           type: number
 *           description: Number of completed projects
 *         isAvailable:
 *           type: boolean
 *           description: Availability status
 *         isVerified:
 *           type: boolean
 *           description: Verification status
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     ExpertResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           $ref: '#/components/schemas/ExpertProfile'
 *
 *     ExpertsListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             experts:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ExpertProfile'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: number
 *                 limit:
 *                   type: number
 *                 total:
 *                   type: number
 *                 totalPages:
 *                   type: number
 */
/**
 * @swagger
 * /api/v1/experts:
 *   post:
 *     summary: Create expert profile
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bio
 *               - skills
 *               - experience
 *               - location
 *             properties:
 *               bio:
 *                 type: string
 *                 minLength: 50
 *                 maxLength: 1000
 *                 example: "مطور ويب متخصص في React و Node.js مع خبرة 5 سنوات في تطوير التطبيقات الحديثة"
 *               skills:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["React", "Node.js", "JavaScript", "TypeScript"]
 *               experience:
 *                 type: number
 *                 minimum: 0
 *                 example: 5
 *               hourlyRate:
 *                 type: number
 *                 minimum: 0
 *                 example: 25
 *               availability:
 *                 type: string
 *                 enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *                 example: "FLEXIBLE"
 *               location:
 *                 type: object
 *                 properties:
 *                   governorate:
 *                     type: string
 *                     example: "دمشق"
 *                   city:
 *                     type: string
 *                     example: "دمشق"
 *     responses:
 *       201:
 *         description: Expert profile created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       400:
 *         description: Validation error or profile already exists
 *       401:
 *         description: Authentication required
 */
router.post('/', auth_1.authenticate, (0, validation_1.validateBody)(createExpertProfileSchema), expertsController.createExpertProfile);
/**
 * @swagger
 * /api/v1/experts:
 *   get:
 *     summary: Search and list experts
 *     tags: [Experts]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: skills
 *         schema:
 *           type: string
 *         description: Comma-separated skills
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Filter by governorate
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Filter by city
 *       - in: query
 *         name: availability
 *         schema:
 *           type: string
 *           enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *         description: Filter by availability
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *       - in: query
 *         name: isVerified
 *         schema:
 *           type: boolean
 *         description: Filter by verification status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, experience, hourly_rate, total_reviews, created_at]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Experts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertsListResponse'
 */
router.get('/', (0, validation_1.validateQuery)(searchExpertsSchema), expertsController.searchExperts);
/**
 * @swagger
 * /api/v1/experts/{id}:
 *   get:
 *     summary: Get expert profile by ID
 *     tags: [Experts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Expert profile ID
 *     responses:
 *       200:
 *         description: Expert profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       404:
 *         description: Expert profile not found
 */
router.get('/:id', expertsController.getExpertById);
/**
 * @swagger
 * /api/v1/experts/{id}:
 *   put:
 *     summary: Update expert profile
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Expert profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bio:
 *                 type: string
 *                 minLength: 50
 *                 maxLength: 1000
 *               skills:
 *                 type: array
 *                 items:
 *                   type: string
 *               hourlyRate:
 *                 type: number
 *                 minimum: 0
 *               availability:
 *                 type: string
 *                 enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *               isAvailable:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Expert profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - not profile owner
 *       404:
 *         description: Expert profile not found
 */
router.put('/:id', auth_1.authenticate, auth_1.expertOrAdmin, (0, validation_1.validateBody)(updateExpertProfileSchema), expertsController.updateExpertProfile);
/**
 * @swagger
 * /api/v1/experts/me:
 *   get:
 *     summary: Get current user's expert profile
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Expert profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Expert profile not found
 */
router.get('/me', auth_1.authenticate, expertsController.getMyExpertProfile);
/**
 * @swagger
 * /api/v1/experts/me:
 *   put:
 *     summary: Update current user's expert profile
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bio:
 *                 type: string
 *               skills:
 *                 type: array
 *                 items:
 *                   type: string
 *               hourlyRate:
 *                 type: number
 *               availability:
 *                 type: string
 *                 enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *     responses:
 *       200:
 *         description: Expert profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Expert profile not found
 */
router.put('/me', auth_1.authenticate, (0, validation_1.validateBody)(updateExpertProfileSchema), expertsController.updateMyExpertProfile);
/**
 * @swagger
 * /api/v1/experts/{id}/verify:
 *   post:
 *     summary: Verify expert profile (Admin only)
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Expert profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isVerified:
 *                 type: boolean
 *                 example: true
 *               verificationNotes:
 *                 type: string
 *                 example: "Profile verified - all documents checked"
 *     responses:
 *       200:
 *         description: Expert verification status updated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ExpertResponse'
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Expert profile not found
 */
router.post('/:id/verify', auth_1.authenticate, auth_1.adminOnly, expertsController.verifyExpert);
exports.default = router;
//# sourceMappingURL=experts.js.map