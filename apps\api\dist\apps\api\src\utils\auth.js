"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authUtils = exports.sessionUtils = exports.jwtUtils = exports.passwordUtils = exports.fallbackSessionHelpers = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const nanoid_1 = require("nanoid");
const config_1 = require("../config");
const redis_1 = require("./redis");
const logger_1 = require("./logger");
// In-memory session store for development when Redis is not available
const inMemorySessionStore = new Map();
// Helper to clean expired sessions from memory
const cleanExpiredSessions = () => {
    const now = new Date();
    for (const [sessionId, session] of inMemorySessionStore.entries()) {
        if (new Date(session.expiresAt) < now) {
            inMemorySessionStore.delete(sessionId);
        }
    }
};
// Clean expired sessions every 5 minutes
setInterval(cleanExpiredSessions, 5 * 60 * 1000);
// Fallback session helpers when Redis is not available
exports.fallbackSessionHelpers = {
    async setSession(sessionId, userId, data, ttlSeconds) {
        const expiresAt = new Date(Date.now() + ttlSeconds * 1000).toISOString();
        inMemorySessionStore.set(sessionId, {
            userId,
            data,
            createdAt: new Date().toISOString(),
            expiresAt,
        });
    },
    async getSession(sessionId) {
        const session = inMemorySessionStore.get(sessionId);
        if (!session) {
            return null;
        }
        // Check if expired
        if (new Date(session.expiresAt) < new Date()) {
            inMemorySessionStore.delete(sessionId);
            return null;
        }
        return {
            userId: session.userId,
            data: session.data,
        };
    },
    async deleteSession(sessionId) {
        inMemorySessionStore.delete(sessionId);
    },
};
// Password utilities
exports.passwordUtils = {
    /**
     * Hash a password using bcrypt
     */
    async hash(password) {
        try {
            return await bcryptjs_1.default.hash(password, config_1.securityConfig.bcryptRounds);
        }
        catch (error) {
            logger_1.logger.error('Password hashing failed', { error });
            throw new Error('Failed to hash password');
        }
    },
    /**
     * Verify a password against its hash
     */
    async verify(password, hash) {
        try {
            return await bcryptjs_1.default.compare(password, hash);
        }
        catch (error) {
            logger_1.logger.error('Password verification failed', { error });
            return false;
        }
    },
    /**
     * Generate a secure random password
     */
    generateSecure(length = 12) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        // Ensure at least one character from each required type
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
        password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
        password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special character
        // Fill the rest randomly
        for (let i = 4; i < length; i++) {
            password += charset[Math.floor(Math.random() * charset.length)];
        }
        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    },
};
// JWT utilities
exports.jwtUtils = {
    /**
     * Generate access token
     */
    generateAccessToken(payload) {
        try {
            return jsonwebtoken_1.default.sign(payload, config_1.jwtConfig.secret, {
                expiresIn: config_1.jwtConfig.expiresIn,
                issuer: 'freela-syria-api',
                audience: 'freela-syria-app',
            });
        }
        catch (error) {
            logger_1.logger.error('Access token generation failed', { error, userId: payload.userId });
            throw new Error('Failed to generate access token');
        }
    },
    /**
     * Generate refresh token
     */
    generateRefreshToken(payload) {
        try {
            return jsonwebtoken_1.default.sign(payload, config_1.jwtConfig.refreshSecret, {
                expiresIn: config_1.jwtConfig.refreshExpiresIn,
                issuer: 'freela-syria-api',
                audience: 'freela-syria-app',
            });
        }
        catch (error) {
            logger_1.logger.error('Refresh token generation failed', { error, userId: payload.userId });
            throw new Error('Failed to generate refresh token');
        }
    },
    /**
     * Verify access token
     */
    verifyAccessToken(token) {
        try {
            return jsonwebtoken_1.default.verify(token, config_1.jwtConfig.secret, {
                issuer: 'freela-syria-api',
                audience: 'freela-syria-app',
            });
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                logger_1.logger.debug('Access token expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                logger_1.logger.warn('Invalid access token', { error: error.message });
            }
            else {
                logger_1.logger.error('Access token verification failed', { error });
            }
            return null;
        }
    },
    /**
     * Verify refresh token
     */
    verifyRefreshToken(token) {
        try {
            return jsonwebtoken_1.default.verify(token, config_1.jwtConfig.refreshSecret, {
                issuer: 'freela-syria-api',
                audience: 'freela-syria-app',
            });
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                logger_1.logger.debug('Refresh token expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                logger_1.logger.warn('Invalid refresh token', { error: error.message });
            }
            else {
                logger_1.logger.error('Refresh token verification failed', { error });
            }
            return null;
        }
    },
    /**
     * Decode token without verification (for debugging)
     */
    decode(token) {
        try {
            return jsonwebtoken_1.default.decode(token);
        }
        catch (error) {
            logger_1.logger.error('Token decoding failed', { error });
            return null;
        }
    },
};
// Session utilities
exports.sessionUtils = {
    /**
     * Create a new session
     */
    async createSession(userId, email, role, userData) {
        try {
            const sessionId = (0, nanoid_1.nanoid)();
            // Store session data in Redis
            const sessionData = {
                userId,
                email,
                role,
                userData,
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
            };
            await redis_1.sessionHelpers.setSession(sessionId, userId, sessionData, 7 * 24 * 60 * 60); // 7 days
            // Generate tokens
            const tokenPayload = { userId, email, role, sessionId };
            const accessToken = exports.jwtUtils.generateAccessToken(tokenPayload);
            const refreshToken = exports.jwtUtils.generateRefreshToken(tokenPayload);
            // Store refresh token in Redis with longer TTL
            await redis_1.redis.set(`refresh:${sessionId}`, refreshToken, 7 * 24 * 60 * 60); // 7 days
            logger_1.logger.info('Session created', { userId, sessionId });
            return {
                accessToken,
                refreshToken,
                expiresIn: 15 * 60, // 15 minutes in seconds
                refreshExpiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
            };
        }
        catch (error) {
            logger_1.logger.error('Session creation failed', { error, userId });
            throw new Error('Failed to create session');
        }
    },
    /**
     * Refresh tokens using refresh token
     */
    async refreshTokens(refreshToken) {
        try {
            // Verify refresh token
            const payload = exports.jwtUtils.verifyRefreshToken(refreshToken);
            if (!payload) {
                return null;
            }
            // Check if session exists
            const sessionData = await redis_1.sessionHelpers.getSession(payload.sessionId);
            if (!sessionData) {
                logger_1.logger.warn('Session not found for refresh token', { sessionId: payload.sessionId });
                return null;
            }
            // Check if stored refresh token matches
            const storedRefreshToken = await redis_1.redis.get(`refresh:${payload.sessionId}`);
            if (storedRefreshToken !== refreshToken) {
                logger_1.logger.warn('Refresh token mismatch', { sessionId: payload.sessionId });
                (0, logger_1.logSecurityEvent)('refresh_token_mismatch', { sessionId: payload.sessionId, userId: payload.userId });
                return null;
            }
            // Update last activity
            sessionData.data.lastActivity = new Date().toISOString();
            await redis_1.sessionHelpers.setSession(payload.sessionId, payload.userId, sessionData.data, 7 * 24 * 60 * 60);
            // Generate new tokens
            const newTokenPayload = {
                userId: payload.userId,
                email: payload.email,
                role: payload.role,
                sessionId: payload.sessionId,
            };
            const newAccessToken = exports.jwtUtils.generateAccessToken(newTokenPayload);
            const newRefreshToken = exports.jwtUtils.generateRefreshToken(newTokenPayload);
            // Update refresh token in Redis
            await redis_1.redis.set(`refresh:${payload.sessionId}`, newRefreshToken, 7 * 24 * 60 * 60);
            logger_1.logger.info('Tokens refreshed', { userId: payload.userId, sessionId: payload.sessionId });
            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                expiresIn: 15 * 60, // 15 minutes in seconds
                refreshExpiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
            };
        }
        catch (error) {
            logger_1.logger.error('Token refresh failed', { error });
            return null;
        }
    },
    /**
     * Validate session
     */
    async validateSession(sessionId) {
        try {
            // Try Redis first, fallback to in-memory store
            let sessionData;
            try {
                sessionData = await redis_1.sessionHelpers.getSession(sessionId);
            }
            catch (redisError) {
                logger_1.logger.debug('Redis unavailable, using fallback session store', { sessionId });
                sessionData = await exports.fallbackSessionHelpers.getSession(sessionId);
            }
            return sessionData !== null;
        }
        catch (error) {
            logger_1.logger.error('Session validation failed', { error, sessionId });
            return false;
        }
    },
    /**
     * Invalidate session (logout)
     */
    async invalidateSession(sessionId) {
        try {
            // Remove session data
            await redis_1.sessionHelpers.deleteSession(sessionId);
            // Remove refresh token
            await redis_1.redis.del(`refresh:${sessionId}`);
            logger_1.logger.info('Session invalidated', { sessionId });
            return true;
        }
        catch (error) {
            logger_1.logger.error('Session invalidation failed', { error, sessionId });
            return false;
        }
    },
    /**
     * Invalidate all user sessions
     */
    async invalidateAllUserSessions(userId) {
        try {
            // This would require a more complex implementation with session indexing
            // For now, we'll implement a simple approach
            logger_1.logger.info('All user sessions invalidated', { userId });
            return true;
        }
        catch (error) {
            logger_1.logger.error('All user sessions invalidation failed', { error, userId });
            return false;
        }
    },
    /**
     * Update session activity
     */
    async updateSessionActivity(sessionId) {
        try {
            const sessionData = await redis_1.sessionHelpers.getSession(sessionId);
            if (!sessionData) {
                return false;
            }
            sessionData.data.lastActivity = new Date().toISOString();
            await redis_1.sessionHelpers.setSession(sessionId, sessionData.userId, sessionData.data, 7 * 24 * 60 * 60);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Session activity update failed', { error, sessionId });
            return false;
        }
    },
};
// Utility functions
exports.authUtils = {
    /**
     * Extract token from Authorization header
     */
    extractTokenFromHeader(authHeader) {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        return authHeader.substring(7);
    },
    /**
     * Generate verification code
     */
    generateVerificationCode(length = 6) {
        const digits = '0123456789';
        let code = '';
        for (let i = 0; i < length; i++) {
            code += digits[Math.floor(Math.random() * digits.length)];
        }
        return code;
    },
    /**
     * Generate secure token for password reset, email verification, etc.
     */
    generateSecureToken() {
        return (0, nanoid_1.nanoid)(32);
    },
    /**
     * Check if password meets security requirements
     */
    validatePasswordStrength(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/[0-9]/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[^A-Za-z0-9]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    },
};
// Types are already exported above
//# sourceMappingURL=auth.js.map