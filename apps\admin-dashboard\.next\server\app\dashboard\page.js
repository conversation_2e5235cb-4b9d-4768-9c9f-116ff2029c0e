/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b784\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FtZXJrJTVDRG9jdW1lbnRzJTVDRnJlZWxhJTVDYXBwcyU1Q2FkbWluLWRhc2hib2FyZCU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q2xheW91dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvPzc1ZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcRnJlZWxhXFxcXGFwcHNcXFxcYWRtaW4tZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FtZXJrJTVDRG9jdW1lbnRzJTVDRnJlZWxhJTVDYXBwcyU1Q2FkbWluLWRhc2hib2FyZCU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLz80MjI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYW1lcmtcXFxcRG9jdW1lbnRzXFxcXEZyZWVsYVxcXFxhcHBzXFxcXGFkbWluLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cerror.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cerror.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FtZXJrJTVDRG9jdW1lbnRzJTVDRnJlZWxhJTVDYXBwcyU1Q2FkbWluLWRhc2hib2FyZCU1Q3NyYyU1Q2FwcCU1Q2Vycm9yLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8/OWVkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFtZXJrXFxcXERvY3VtZW50c1xcXFxGcmVlbGFcXFxcYXBwc1xcXFxhZG1pbi1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cerror.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FtZXJrJTVDRG9jdW1lbnRzJTVDRnJlZWxhJTVDYXBwcyU1Q2FkbWluLWRhc2hib2FyZCU1Q3NyYyU1Q2FwcCU1Q3Byb3ZpZGVycy50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNhbWVyayU1Q0RvY3VtZW50cyU1Q0ZyZWVsYSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1pbnRlciUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYW1lcmslNUNEb2N1bWVudHMlNUNGcmVlbGElNUNhcHBzJTVDYWRtaW4tZGFzaGJvYXJkJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvPzE5NDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcRnJlZWxhXFxcXGFwcHNcXFxcYWRtaW4tZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccHJvdmlkZXJzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cproviders.tsx&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/link.js */ \"(ssr)/../../node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q2FtZXJrJTVDRG9jdW1lbnRzJTVDRnJlZWxhJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLz8yMGI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYW1lcmtcXFxcRG9jdW1lbnRzXFxcXEZyZWVsYVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                open: sidebarOpen,\n                setOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pr-64 rtl:lg:pr-0 rtl:lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        onMenuClick: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_StatsCards__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/StatsCards */ \"(ssr)/./src/components/dashboard/StatsCards.tsx\");\n/* harmony import */ var _components_dashboard_RecentActivity__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/RecentActivity */ \"(ssr)/./src/components/dashboard/RecentActivity.tsx\");\n/* harmony import */ var _components_dashboard_UserGrowthChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/UserGrowthChart */ \"(ssr)/./src/components/dashboard/UserGrowthChart.tsx\");\n/* harmony import */ var _components_dashboard_RevenueChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/RevenueChart */ \"(ssr)/./src/components/dashboard/RevenueChart.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                        children: \"لوحة التحكم الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                        children: \"نظرة عامة على منصة فريلا سوريا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StatsCards__WEBPACK_IMPORTED_MODULE_1__.StatsCards, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserGrowthChart__WEBPACK_IMPORTED_MODULE_3__.UserGrowthChart, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RevenueChart__WEBPACK_IMPORTED_MODULE_4__.RevenueChart, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RecentActivity__WEBPACK_IMPORTED_MODULE_2__.RecentActivity, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.error(error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-9xl font-bold text-gray-300 dark:text-gray-700\",\n                            children: \"500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900 dark:text-white\",\n                            children: \"حدث خطأ ما\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600 dark:text-gray-400\",\n                            children: \"عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                            children: \"المحاولة مرة أخرى\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700\",\n                                children: \"العودة إلى لوحة التحكم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"dark\",\n            enableSystem: false,\n            disableTransitionOnChange: true,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-center\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"#1f2937\",\n                            color: \"#f9fafb\",\n                            border: \"1px solid #374151\"\n                        },\n                        success: {\n                            iconTheme: {\n                                primary: \"#10b981\",\n                                secondary: \"#f9fafb\"\n                            }\n                        },\n                        error: {\n                            iconTheme: {\n                                primary: \"#ef4444\",\n                                secondary: \"#f9fafb\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/RecentActivity.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/RecentActivity.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentActivity: () => (/* binding */ RecentActivity)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ RecentActivity auto */ \n\nconst activities = [\n    {\n        id: 1,\n        type: \"user_registration\",\n        message: \"مستخدم جديد سجل في المنصة\",\n        user: \"أحمد محمد\",\n        time: \"منذ 5 دقائق\",\n        status: \"success\"\n    },\n    {\n        id: 2,\n        type: \"service_published\",\n        message: \"خدمة جديدة تم نشرها\",\n        user: \"فاطمة أحمد\",\n        time: \"منذ 15 دقيقة\",\n        status: \"success\"\n    },\n    {\n        id: 3,\n        type: \"payment_failed\",\n        message: \"فشل في عملية دفع\",\n        user: \"محمد علي\",\n        time: \"منذ 30 دقيقة\",\n        status: \"error\"\n    },\n    {\n        id: 4,\n        type: \"booking_pending\",\n        message: \"حجز جديد في انتظار الموافقة\",\n        user: \"سارة خالد\",\n        time: \"منذ ساعة\",\n        status: \"warning\"\n    },\n    {\n        id: 5,\n        type: \"expert_verified\",\n        message: \"تم التحقق من خبير جديد\",\n        user: \"عمر حسن\",\n        time: \"منذ ساعتين\",\n        status: \"success\"\n    }\n];\nconst statusIcons = {\n    success: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    error: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    warning: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n};\nconst statusColors = {\n    success: \"text-green-500\",\n    error: \"text-red-500\",\n    warning: \"text-yellow-500\"\n};\nfunction RecentActivity() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                        children: \"النشاطات الأخيرة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                        children: \"آخر الأحداث والنشاطات على المنصة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                children: activities.map((activity)=>{\n                    const Icon = statusIcons[activity.status];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: `h-5 w-5 ${statusColors[activity.status]}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                            children: activity.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"بواسطة \",\n                                                activity.user,\n                                                \" • \",\n                                                activity.time\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, this)\n                    }, activity.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    className: \"text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500\",\n                    children: \"عرض جميع النشاطات\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RecentActivity.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/RecentActivity.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/RevenueChart.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RevenueChart.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RevenueChart: () => (/* binding */ RevenueChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ RevenueChart auto */ \n\nconst data = [\n    {\n        month: \"يناير\",\n        revenue: 4000,\n        commissions: 400\n    },\n    {\n        month: \"فبراير\",\n        revenue: 3000,\n        commissions: 300\n    },\n    {\n        month: \"مارس\",\n        revenue: 2000,\n        commissions: 200\n    },\n    {\n        month: \"أبريل\",\n        revenue: 2780,\n        commissions: 278\n    },\n    {\n        month: \"مايو\",\n        revenue: 1890,\n        commissions: 189\n    },\n    {\n        month: \"يونيو\",\n        revenue: 2390,\n        commissions: 239\n    },\n    {\n        month: \"يوليو\",\n        revenue: 3490,\n        commissions: 349\n    }\n];\nfunction RevenueChart() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                        children: \"الإيرادات والعمولات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                        children: \"إحصائيات الإيرادات الشهرية بالدولار الأمريكي\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.BarChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                className: \"stroke-gray-200 dark:stroke-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.XAxis, {\n                                dataKey: \"month\",\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                tick: {\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.YAxis, {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                tick: {\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                contentStyle: {\n                                    backgroundColor: \"rgb(31 41 55)\",\n                                    border: \"1px solid rgb(75 85 99)\",\n                                    borderRadius: \"0.5rem\",\n                                    color: \"rgb(243 244 246)\"\n                                },\n                                formatter: (value)=>[\n                                        `$${value}`,\n                                        \"\"\n                                    ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Bar, {\n                                dataKey: \"revenue\",\n                                fill: \"#3b82f6\",\n                                name: \"إجمالي الإيرادات\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Bar, {\n                                dataKey: \"commissions\",\n                                fill: \"#10b981\",\n                                name: \"عمولات المنصة\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\RevenueChart.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/RevenueChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/StatsCards.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/StatsCards.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCards: () => (/* binding */ StatsCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CurrencyDollarIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CurrencyDollarIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CurrencyDollarIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BriefcaseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CurrencyDollarIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ StatsCards auto */ \n\nconst stats = [\n    {\n        name: \"إجمالي المستخدمين\",\n        value: \"2,847\",\n        change: \"+12%\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    {\n        name: \"الخبراء النشطين\",\n        value: \"1,234\",\n        change: \"+8%\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        name: \"الخدمات المنشورة\",\n        value: \"5,678\",\n        change: \"+15%\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"الإيرادات الشهرية\",\n        value: \"$45,231\",\n        change: \"+23%\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CurrencyDollarIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    }\n];\nfunction StatsCards() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n        children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:px-6 sm:py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute rounded-md bg-primary-500 p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                    className: \"h-6 w-6 text-white\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mr-16 rtl:mr-0 rtl:ml-16 truncate text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                children: stat.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                        className: \"mr-16 rtl:mr-0 rtl:ml-16 flex items-baseline pb-6 sm:pb-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-semibold text-gray-900 dark:text-white\",\n                                children: stat.value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `mr-2 rtl:mr-0 rtl:ml-2 flex items-baseline text-sm font-semibold ${stat.changeType === \"increase\" ? \"text-green-600 dark:text-green-400\" : \"text-red-600 dark:text-red-400\"}`,\n                                children: stat.change\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-x-0 bottom-0 bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-primary-600 dark:text-primary-400\",\n                                        children: \"مقارنة بالشهر الماضي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, stat.name, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\StatsCards.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/StatsCards.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/UserGrowthChart.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/UserGrowthChart.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserGrowthChart: () => (/* binding */ UserGrowthChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ UserGrowthChart auto */ \n\nconst data = [\n    {\n        month: \"يناير\",\n        users: 400,\n        experts: 240\n    },\n    {\n        month: \"فبراير\",\n        users: 300,\n        experts: 139\n    },\n    {\n        month: \"مارس\",\n        users: 200,\n        experts: 980\n    },\n    {\n        month: \"أبريل\",\n        users: 278,\n        experts: 390\n    },\n    {\n        month: \"مايو\",\n        users: 189,\n        experts: 480\n    },\n    {\n        month: \"يونيو\",\n        users: 239,\n        experts: 380\n    },\n    {\n        month: \"يوليو\",\n        users: 349,\n        experts: 430\n    }\n];\nfunction UserGrowthChart() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                        children: \"نمو المستخدمين والخبراء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                        children: \"إحصائيات النمو الشهري للمنصة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.LineChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                className: \"stroke-gray-200 dark:stroke-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.XAxis, {\n                                dataKey: \"month\",\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                tick: {\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.YAxis, {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                tick: {\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                contentStyle: {\n                                    backgroundColor: \"rgb(31 41 55)\",\n                                    border: \"1px solid rgb(75 85 99)\",\n                                    borderRadius: \"0.5rem\",\n                                    color: \"rgb(243 244 246)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"users\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: 2,\n                                name: \"المستخدمين\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"experts\",\n                                stroke: \"#10b981\",\n                                strokeWidth: 2,\n                                name: \"الخبراء\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\dashboard\\\\UserGrowthChart.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/UserGrowthChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/../../node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/../../node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\nfunction Header({ onMenuClick }) {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fix hydration mismatch by ensuring component is mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"-m-2.5 p-2.5 text-gray-700 dark:text-gray-300 lg:hidden\",\n                onClick: onMenuClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"فتح الشريط الجانبي\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-6 w-6\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-6 w-px bg-gray-200 dark:bg-gray-700 lg:hidden\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300\",\n                                onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"تبديل المظهر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this),\n                                    mounted && (theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)),\n                                    !mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"عرض الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200 dark:lg:bg-gray-700\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Menu, {\n                                as: \"div\",\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Menu.Button, {\n                                        className: \"-m-1.5 flex items-center p-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"فتح قائمة المستخدم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 rounded-full bg-gray-50 dark:bg-gray-800 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: \"أ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:flex lg:items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-4 rtl:mr-0 rtl:ml-4 text-sm font-semibold leading-6 text-gray-900 dark:text-white\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"المدير العام\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__.Transition, {\n                                        as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                        enter: \"transition ease-out duration-100\",\n                                        enterFrom: \"transform opacity-0 scale-95\",\n                                        enterTo: \"transform opacity-100 scale-100\",\n                                        leave: \"transition ease-in duration-75\",\n                                        leaveFrom: \"transform opacity-100 scale-100\",\n                                        leaveTo: \"transform opacity-0 scale-95\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Menu.Items, {\n                                            className: \"absolute left-0 rtl:left-auto rtl:right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white dark:bg-gray-800 py-2 shadow-lg ring-1 ring-gray-900/5 dark:ring-gray-700 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(active ? \"bg-gray-50 dark:bg-gray-700\" : \"\", \"block px-3 py-1 text-sm leading-6 text-gray-900 dark:text-white\"),\n                                                            children: \"الملف الشخصي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(active ? \"bg-gray-50 dark:bg-gray-700\" : \"\", \"block px-3 py-1 text-sm leading-6 text-gray-900 dark:text-white\"),\n                                                            children: \"تسجيل الخروج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVxRjtBQUM5QztBQUNjO0FBQ0M7QUFDMUI7QUFNckIsU0FBU1csT0FBTyxFQUFFQyxXQUFXLEVBQWU7SUFDakQsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHVixxREFBUUE7SUFDcEMsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBRXZDLDBEQUEwRDtJQUMxREMsZ0RBQVNBLENBQUM7UUFDUk8sV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQ0NDLE1BQUs7Z0JBQ0xGLFdBQVU7Z0JBQ1ZHLFNBQVNUOztrQ0FFVCw4REFBQ1U7d0JBQUtKLFdBQVU7a0NBQVU7Ozs7OztrQ0FDMUIsOERBQUNsQiw2SEFBU0E7d0JBQUNrQixXQUFVO3dCQUFVSyxlQUFZOzs7Ozs7Ozs7Ozs7MEJBSTdDLDhEQUFDTjtnQkFBSUMsV0FBVTtnQkFBa0RLLGVBQVk7Ozs7OzswQkFFN0UsOERBQUNOO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDQztnQ0FDQ0MsTUFBSztnQ0FDTEYsV0FBVTtnQ0FDVkcsU0FBUyxJQUFNUCxTQUFTRCxVQUFVLFNBQVMsVUFBVTs7a0RBRXJELDhEQUFDUzt3Q0FBS0osV0FBVTtrREFBVTs7Ozs7O29DQUV6QkgsV0FDQ0YsQ0FBQUEsVUFBVSx1QkFDUiw4REFBQ1YsNkhBQU9BO3dDQUFDZSxXQUFVO3dDQUFVSyxlQUFZOzs7Ozs2REFFekMsOERBQUNyQiw2SEFBUUE7d0NBQUNnQixXQUFVO3dDQUFVSyxlQUFZOzs7Ozs0Q0FDNUM7b0NBR0QsQ0FBQ1IseUJBQ0EsOERBQUNiLDZIQUFRQTt3Q0FBQ2dCLFdBQVU7d0NBQVVLLGVBQVk7Ozs7Ozs7Ozs7OzswQ0FLOUMsOERBQUNKO2dDQUNDQyxNQUFLO2dDQUNMRixXQUFVOztrREFFViw4REFBQ0k7d0NBQUtKLFdBQVU7a0RBQVU7Ozs7OztrREFDMUIsOERBQUNqQiw2SEFBUUE7d0NBQUNpQixXQUFVO3dDQUFVSyxlQUFZOzs7Ozs7Ozs7Ozs7MENBSTVDLDhEQUFDTjtnQ0FBSUMsV0FBVTtnQ0FBb0VLLGVBQVk7Ozs7OzswQ0FHL0YsOERBQUNsQix5RkFBSUE7Z0NBQUNtQixJQUFHO2dDQUFNTixXQUFVOztrREFDdkIsOERBQUNiLHlGQUFJQSxDQUFDb0IsTUFBTTt3Q0FBQ1AsV0FBVTs7MERBQ3JCLDhEQUFDSTtnREFBS0osV0FBVTswREFBVTs7Ozs7OzBEQUMxQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNJO29EQUFLSixXQUFVOzhEQUF1RDs7Ozs7Ozs7Ozs7MERBSXpFLDhEQUFDSTtnREFBS0osV0FBVTswREFDZCw0RUFBQ0k7b0RBQUtKLFdBQVU7b0RBQXVGSyxlQUFZOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrREFLOUgsOERBQUNqQiwrRkFBVUE7d0NBQ1RrQixJQUFJakIsMkNBQVFBO3dDQUNabUIsT0FBTTt3Q0FDTkMsV0FBVTt3Q0FDVkMsU0FBUTt3Q0FDUkMsT0FBTTt3Q0FDTkMsV0FBVTt3Q0FDVkMsU0FBUTtrREFFUiw0RUFBQzFCLHlGQUFJQSxDQUFDMkIsS0FBSzs0Q0FBQ2QsV0FBVTs7OERBQ3BCLDhEQUFDYix5RkFBSUEsQ0FBQzRCLElBQUk7OERBQ1AsQ0FBQyxFQUFFQyxNQUFNLEVBQUUsaUJBQ1YsOERBQUNDOzREQUNDQyxNQUFLOzREQUNMbEIsV0FBV1IsMENBQUlBLENBQ2J3QixTQUFTLGdDQUFnQyxJQUN6QztzRUFFSDs7Ozs7Ozs7Ozs7OERBS0wsOERBQUM3Qix5RkFBSUEsQ0FBQzRCLElBQUk7OERBQ1AsQ0FBQyxFQUFFQyxNQUFNLEVBQUUsaUJBQ1YsOERBQUNDOzREQUNDQyxNQUFLOzREQUNMbEIsV0FBV1IsMENBQUlBLENBQ2J3QixTQUFTLGdDQUFnQyxJQUN6QztzRUFFSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVlyQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlci50c3g/MDY4YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IEJhcnMzSWNvbiwgQmVsbEljb24sIE1vb25JY29uLCBTdW5JY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgTWVudSwgVHJhbnNpdGlvbiB9IGZyb20gJ0BoZWFkbGVzc3VpL3JlYWN0JztcbmltcG9ydCB7IEZyYWdtZW50LCB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY2xzeCB9IGZyb20gJ2Nsc3gnO1xuXG5pbnRlcmZhY2UgSGVhZGVyUHJvcHMge1xuICBvbk1lbnVDbGljazogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEhlYWRlcih7IG9uTWVudUNsaWNrIH06IEhlYWRlclByb3BzKSB7XG4gIGNvbnN0IHsgdGhlbWUsIHNldFRoZW1lIH0gPSB1c2VUaGVtZSgpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gRml4IGh5ZHJhdGlvbiBtaXNtYXRjaCBieSBlbnN1cmluZyBjb21wb25lbnQgaXMgbW91bnRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC0wIHotNDAgZmxleCBoLTE2IHNocmluay0wIGl0ZW1zLWNlbnRlciBnYXAteC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwIHB4LTQgc2hhZG93LXNtIHNtOmdhcC14LTYgc206cHgtNiBsZzpweC04XCI+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICBjbGFzc05hbWU9XCItbS0yLjUgcC0yLjUgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbGc6aGlkZGVuXCJcbiAgICAgICAgb25DbGljaz17b25NZW51Q2xpY2t9XG4gICAgICA+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj7Zgdiq2K0g2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKPC9zcGFuPlxuICAgICAgICA8QmFyczNJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgPC9idXR0b24+XG5cbiAgICAgIHsvKiBTZXBhcmF0b3IgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNiB3LXB4IGJnLWdyYXktMjAwIGRhcms6YmctZ3JheS03MDAgbGc6aGlkZGVuXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtMSBnYXAteC00IHNlbGYtc3RyZXRjaCBsZzpnYXAteC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBmbGV4LTFcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAteC00IGxnOmdhcC14LTZcIj5cbiAgICAgICAgICB7LyogVGhlbWUgdG9nZ2xlICovfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiLW0tMi41IHAtMi41IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTUwMCBkYXJrOmhvdmVyOnRleHQtZ3JheS0zMDBcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0VGhlbWUodGhlbWUgPT09ICdkYXJrJyA/ICdsaWdodCcgOiAnZGFyaycpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj7Yqtio2K/ZitmEINin2YTZhdi42YfYsTwvc3Bhbj5cbiAgICAgICAgICAgIHsvKiBGaXggaHlkcmF0aW9uIG1pc21hdGNoIGJ5IGNoZWNraW5nIGlmIGNvbXBvbmVudCBpcyBtb3VudGVkICovfVxuICAgICAgICAgICAge21vdW50ZWQgJiYgKFxuICAgICAgICAgICAgICB0aGVtZSA9PT0gJ2RhcmsnID8gKFxuICAgICAgICAgICAgICAgIDxTdW5JY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxNb29uSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHsvKiBTaG93IGRlZmF1bHQgaWNvbiBkdXJpbmcgU1NSICovfVxuICAgICAgICAgICAgeyFtb3VudGVkICYmIChcbiAgICAgICAgICAgICAgPE1vb25JY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiBOb3RpZmljYXRpb25zICovfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiLW0tMi41IHAtMi41IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTUwMCBkYXJrOmhvdmVyOnRleHQtZ3JheS0zMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj7Yudix2LYg2KfZhNil2LTYudin2LHYp9iqPC9zcGFuPlxuICAgICAgICAgICAgPEJlbGxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgey8qIFNlcGFyYXRvciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpibG9jayBsZzpoLTYgbGc6dy1weCBsZzpiZy1ncmF5LTIwMCBkYXJrOmxnOmJnLWdyYXktNzAwXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cblxuICAgICAgICAgIHsvKiBQcm9maWxlIGRyb3Bkb3duICovfVxuICAgICAgICAgIDxNZW51IGFzPVwiZGl2XCIgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxNZW51LkJ1dHRvbiBjbGFzc05hbWU9XCItbS0xLjUgZmxleCBpdGVtcy1jZW50ZXIgcC0xLjVcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPtmB2KrYrSDZgtin2KbZhdipINin2YTZhdiz2KrYrtiv2YU8L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IHctOCByb3VuZGVkLWZ1bGwgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktODAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAg2KNcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBsZzppdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci00IHJ0bDptci0wIHJ0bDptbC00IHRleHQtc20gZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIiBhcmlhLWhpZGRlbj1cInRydWVcIj5cbiAgICAgICAgICAgICAgICAgINin2YTZhdiv2YrYsSDYp9mE2LnYp9mFXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L01lbnUuQnV0dG9uPlxuICAgICAgICAgICAgPFRyYW5zaXRpb25cbiAgICAgICAgICAgICAgYXM9e0ZyYWdtZW50fVxuICAgICAgICAgICAgICBlbnRlcj1cInRyYW5zaXRpb24gZWFzZS1vdXQgZHVyYXRpb24tMTAwXCJcbiAgICAgICAgICAgICAgZW50ZXJGcm9tPVwidHJhbnNmb3JtIG9wYWNpdHktMCBzY2FsZS05NVwiXG4gICAgICAgICAgICAgIGVudGVyVG89XCJ0cmFuc2Zvcm0gb3BhY2l0eS0xMDAgc2NhbGUtMTAwXCJcbiAgICAgICAgICAgICAgbGVhdmU9XCJ0cmFuc2l0aW9uIGVhc2UtaW4gZHVyYXRpb24tNzVcIlxuICAgICAgICAgICAgICBsZWF2ZUZyb209XCJ0cmFuc2Zvcm0gb3BhY2l0eS0xMDAgc2NhbGUtMTAwXCJcbiAgICAgICAgICAgICAgbGVhdmVUbz1cInRyYW5zZm9ybSBvcGFjaXR5LTAgc2NhbGUtOTVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TWVudS5JdGVtcyBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgcnRsOmxlZnQtYXV0byBydGw6cmlnaHQtMCB6LTEwIG10LTIuNSB3LTMyIG9yaWdpbi10b3AtcmlnaHQgcm91bmRlZC1tZCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHB5LTIgc2hhZG93LWxnIHJpbmctMSByaW5nLWdyYXktOTAwLzUgZGFyazpyaW5nLWdyYXktNzAwIGZvY3VzOm91dGxpbmUtbm9uZVwiPlxuICAgICAgICAgICAgICAgIDxNZW51Lkl0ZW0+XG4gICAgICAgICAgICAgICAgICB7KHsgYWN0aXZlIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiI1wiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlID8gJ2JnLWdyYXktNTAgZGFyazpiZy1ncmF5LTcwMCcgOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICdibG9jayBweC0zIHB5LTEgdGV4dC1zbSBsZWFkaW5nLTYgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgINin2YTZhdmE2YEg2KfZhNi02K7YtdmKXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9NZW51Lkl0ZW0+XG4gICAgICAgICAgICAgICAgPE1lbnUuSXRlbT5cbiAgICAgICAgICAgICAgICAgIHsoeyBhY3RpdmUgfSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIjXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmUgPyAnYmctZ3JheS01MCBkYXJrOmJnLWdyYXktNzAwJyA6ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2Jsb2NrIHB4LTMgcHktMSB0ZXh0LXNtIGxlYWRpbmctNiB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg2KrYs9is2YrZhCDYp9mE2K7YsdmI2KxcbiAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L01lbnUuSXRlbT5cbiAgICAgICAgICAgICAgPC9NZW51Lkl0ZW1zPlxuICAgICAgICAgICAgPC9UcmFuc2l0aW9uPlxuICAgICAgICAgIDwvTWVudT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJCYXJzM0ljb24iLCJCZWxsSWNvbiIsIk1vb25JY29uIiwiU3VuSWNvbiIsInVzZVRoZW1lIiwiTWVudSIsIlRyYW5zaXRpb24iLCJGcmFnbWVudCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiY2xzeCIsIkhlYWRlciIsIm9uTWVudUNsaWNrIiwidGhlbWUiLCJzZXRUaGVtZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJzcGFuIiwiYXJpYS1oaWRkZW4iLCJhcyIsIkJ1dHRvbiIsImVudGVyIiwiZW50ZXJGcm9tIiwiZW50ZXJUbyIsImxlYXZlIiwibGVhdmVGcm9tIiwibGVhdmVUbyIsIkl0ZW1zIiwiSXRlbSIsImFjdGl2ZSIsImEiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/../../node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/../../node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BriefcaseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/TagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseIcon,CalendarIcon,ChartBarIcon,CogIcon,CreditCardIcon,DocumentTextIcon,ExclamationTriangleIcon,HomeIcon,TagIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"الرئيسية\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"المستخدمين\",\n        href: \"/dashboard/users\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"الخبراء\",\n        href: \"/dashboard/experts\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"الخدمات\",\n        href: \"/dashboard/services\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"الفئات\",\n        href: \"/dashboard/categories\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"الحجوزات\",\n        href: \"/dashboard/bookings\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"المدفوعات\",\n        href: \"/dashboard/payments\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"النزاعات\",\n        href: \"/dashboard/disputes\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"التحليلات\",\n        href: \"/dashboard/analytics\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"التقارير\",\n        href: \"/dashboard/reports\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: \"الإعدادات\",\n        href: \"/dashboard/settings\",\n        icon: _barrel_optimize_names_BriefcaseIcon_CalendarIcon_ChartBarIcon_CogIcon_CreditCardIcon_DocumentTextIcon_ExclamationTriangleIcon_HomeIcon_TagIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction Sidebar({ open, setOpen }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Root, {\n                show: open,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-50 lg:hidden\",\n                    onClose: setOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"transition-opacity ease-linear duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"transition-opacity ease-linear duration-300\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-900/80\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                enter: \"transition ease-in-out duration-300 transform\",\n                                enterFrom: \"-translate-x-full rtl:translate-x-full\",\n                                enterTo: \"translate-x-0\",\n                                leave: \"transition ease-in-out duration-300 transform\",\n                                leaveFrom: \"translate-x-0\",\n                                leaveTo: \"-translate-x-full rtl:translate-x-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog.Panel, {\n                                    className: \"relative mr-16 rtl:mr-0 rtl:ml-16 flex w-full max-w-xs flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-full rtl:left-auto rtl:right-full top-0 flex w-16 justify-center pt-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"-m-2.5 p-2.5\",\n                                                onClick: ()=>setOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"إغلاق الشريط الجانبي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                            pathname: pathname\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col lg:right-0 rtl:lg:right-auto rtl:lg:left-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                    pathname: pathname\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SidebarContent({ pathname }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-900 px-6 pb-4 border-l rtl:border-l-0 rtl:border-r border-gray-200 dark:border-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 shrink-0 items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                    children: \"فريلا سوريا\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex flex-1 flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    role: \"list\",\n                    className: \"flex flex-1 flex-col gap-y-7\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            role: \"list\",\n                            className: \"-mx-2 space-y-1\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__.clsx)(pathname === item.href ? \"bg-gray-50 dark:bg-gray-800 text-primary-600 dark:text-primary-400\" : \"text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800\", \"group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__.clsx)(pathname === item.href ? \"text-primary-600 dark:text-primary-400\" : \"text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400\", \"h-6 w-6 shrink-0\"),\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1c84cb06f1b9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2ViOWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxYzg0Y2IwNmYxYjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\error.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Freela Syria - Admin Dashboard\",\n    description: \"Administrative dashboard for Freela Syria marketplace\",\n    keywords: [\n        \"freelance\",\n        \"syria\",\n        \"admin\",\n        \"dashboard\"\n    ],\n    authors: [\n        {\n            name: \"Freela Syria Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-arabic antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-9xl font-bold text-gray-300 dark:text-gray-700\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900 dark:text-white\",\n                            children: \"الصفحة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600 dark:text-gray-400\",\n                            children: \"عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        children: \"العودة إلى لوحة التحكم\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\admin-dashboard\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/@headlessui","vendor-chunks/d3-shape","vendor-chunks/d3-scale","vendor-chunks/@heroicons","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/react-smooth","vendor-chunks/react-transition-group","vendor-chunks/@babel","vendor-chunks/prop-types","vendor-chunks/recharts-scale","vendor-chunks/react-is","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant","vendor-chunks/internmap","vendor-chunks/fast-equals","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/clsx","vendor-chunks/object-assign","vendor-chunks/eventemitter3"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Cadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();