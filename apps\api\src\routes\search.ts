import { Router } from 'express';
import { validateQuery } from '../middleware/validation';
import { optionalAuthenticate } from '../middleware/auth';
import * as searchController from '../controllers/search';
import { z } from 'zod';

const router = Router();

// Validation schemas for search
const searchServicesSchema = z.object({
  q: z.string().optional(), // Search query
  category: z.string().optional(),
  subcategory: z.string().optional(),
  serviceType: z.enum(['DIGITAL', 'PHYSICAL']).optional(),
  priceType: z.enum(['FIXED', 'HOURLY', 'NEGOTIABLE']).optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  governorate: z.string().optional(),
  city: z.string().optional(),
  tags: z.string().optional(), // Comma-separated tags
  skills: z.string().optional(), // Comma-separated skills for expert search
  minRating: z.number().min(0).max(5).optional(),
  maxDistance: z.number().min(0).optional(), // For location-based search
  lat: z.number().optional(), // User's latitude
  lng: z.number().optional(), // User's longitude
  sortBy: z.enum(['price', 'rating', 'distance', 'created_at', 'popularity']).default('rating'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20),
  isActive: z.boolean().default(true)
});

const searchExpertsSchema = z.object({
  q: z.string().optional(), // Search query
  skills: z.string().optional(), // Comma-separated skills
  category: z.string().optional(),
  governorate: z.string().optional(),
  city: z.string().optional(),
  availability: z.enum(['FULL_TIME', 'PART_TIME', 'WEEKENDS', 'FLEXIBLE']).optional(),
  minRating: z.number().min(0).max(5).optional(),
  maxHourlyRate: z.number().min(0).optional(),
  minExperience: z.number().min(0).optional(),
  languages: z.string().optional(), // Comma-separated languages
  maxDistance: z.number().min(0).optional(),
  lat: z.number().optional(),
  lng: z.number().optional(),
  isAvailable: z.boolean().default(true),
  isVerified: z.boolean().optional(),
  sortBy: z.enum(['rating', 'experience', 'distance', 'hourly_rate', 'total_reviews']).default('rating'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20)
});

const globalSearchSchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  type: z.enum(['all', 'services', 'experts']).default('all'),
  governorate: z.string().optional(),
  city: z.string().optional(),
  lat: z.number().optional(),
  lng: z.number().optional(),
  maxDistance: z.number().min(0).optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20)
});

/**
 * @swagger
 * components:
 *   schemas:
 *     SearchResult:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [service, expert]
 *           description: Type of search result
 *         id:
 *           type: string
 *           description: Result ID
 *         title:
 *           type: string
 *           description: Result title
 *         description:
 *           type: string
 *           description: Result description
 *         category:
 *           type: string
 *           description: Category
 *         price:
 *           type: number
 *           description: Price (for services) or hourly rate (for experts)
 *         rating:
 *           type: number
 *           description: Average rating
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *         distance:
 *           type: number
 *           description: Distance from user location (if provided)
 *         expert:
 *           type: object
 *           description: Expert information (for services)
 *           properties:
 *             id:
 *               type: string
 *             name:
 *               type: string
 *             rating:
 *               type: number
 *         imageUrl:
 *           type: string
 *           description: Result image URL
 *     
 *     SearchResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             results:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/SearchResult'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: number
 *                 limit:
 *                   type: number
 *                 total:
 *                   type: number
 *                 totalPages:
 *                   type: number
 *             filters:
 *               type: object
 *               description: Applied filters summary
 */

/**
 * @swagger
 * /api/v1/search/services:
 *   get:
 *     summary: Search services with advanced filters
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "تطوير موقع"
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Service category
 *         example: "تطوير الويب"
 *       - in: query
 *         name: serviceType
 *         schema:
 *           type: string
 *           enum: [DIGITAL, PHYSICAL]
 *         description: Type of service
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Governorate filter
 *         example: "دمشق"
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *         example: "دمشق"
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *         description: User latitude for distance calculation
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *         description: User longitude for distance calculation
 *       - in: query
 *         name: maxDistance
 *         schema:
 *           type: number
 *         description: Maximum distance in kilometers
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [price, rating, distance, created_at, popularity]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Services search results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SearchResponse'
 */
router.get('/services',
  optionalAuthenticate,
  validateQuery(searchServicesSchema),
  searchController.searchServices
);

/**
 * @swagger
 * /api/v1/search/experts:
 *   get:
 *     summary: Search experts with advanced filters
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "مطور React"
 *       - in: query
 *         name: skills
 *         schema:
 *           type: string
 *         description: Comma-separated skills
 *         example: "React,Node.js,JavaScript"
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Governorate filter
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *       - in: query
 *         name: availability
 *         schema:
 *           type: string
 *           enum: [FULL_TIME, PART_TIME, WEEKENDS, FLEXIBLE]
 *         description: Availability filter
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *       - in: query
 *         name: maxHourlyRate
 *         schema:
 *           type: number
 *         description: Maximum hourly rate filter
 *       - in: query
 *         name: isVerified
 *         schema:
 *           type: boolean
 *         description: Filter by verification status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, experience, distance, hourly_rate, total_reviews]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Experts search results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SearchResponse'
 */
router.get('/experts',
  optionalAuthenticate,
  validateQuery(searchExpertsSchema),
  searchController.searchExperts
);

/**
 * @swagger
 * /api/v1/search:
 *   get:
 *     summary: Global search across services and experts
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *         example: "تطوير تطبيقات"
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, services, experts]
 *         description: Search type filter
 *       - in: query
 *         name: governorate
 *         schema:
 *           type: string
 *         description: Location filter
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: City filter
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *         description: User latitude for distance-based results
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *         description: User longitude for distance-based results
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Global search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: object
 *                       properties:
 *                         services:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/SearchResult'
 *                         experts:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/SearchResult'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         totalServices:
 *                           type: number
 *                         totalExperts:
 *                           type: number
 *                     query:
 *                       type: string
 *                       description: Original search query
 */
router.get('/',
  optionalAuthenticate,
  validateQuery(globalSearchSchema),
  searchController.globalSearch
);

/**
 * @swagger
 * /api/v1/search/suggestions:
 *   get:
 *     summary: Get search suggestions and autocomplete
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: Partial search query
 *         example: "تطو"
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [services, experts, categories, skills]
 *         description: Type of suggestions
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 20
 *         description: Number of suggestions
 *     responses:
 *       200:
 *         description: Search suggestions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           text:
 *                             type: string
 *                           type:
 *                             type: string
 *                           count:
 *                             type: number
 */
router.get('/suggestions',
  validateQuery(z.object({
    q: z.string().min(2, 'Query must be at least 2 characters'),
    type: z.enum(['services', 'experts', 'categories', 'skills']).optional(),
    limit: z.number().min(1).max(20).default(10)
  })),
  searchController.getSearchSuggestions
);

export default router;
