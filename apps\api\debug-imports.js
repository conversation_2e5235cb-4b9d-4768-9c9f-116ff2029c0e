// Debug script to identify which import is causing the hang
console.log('🔍 Starting import debugging...');

// Test 1: Config import
console.log('1. Testing config import...');
try {
  const config = require('./dist/apps/api/src/config/index.js');
  console.log('✅ Config imported successfully');
  console.log('   - PORT:', config.config.PORT);
  console.log('   - NODE_ENV:', config.config.NODE_ENV);
} catch (error) {
  console.error('❌ Config import failed:', error.message);
  process.exit(1);
}

// Test 2: Logger import
console.log('2. Testing logger import...');
try {
  const logger = require('./dist/apps/api/src/utils/logger.js');
  console.log('✅ Logger imported successfully');
  logger.logger.info('Logger test message');
} catch (error) {
  console.error('❌ Logger import failed:', error.message);
  process.exit(1);
}

// Test 3: Database service import
console.log('3. Testing database service import...');
try {
  const dbService = require('./dist/packages/database/src/services/database-service.js');
  console.log('✅ Database service imported successfully');
  console.log('   - connectDatabase function:', typeof dbService.connectDatabase);
} catch (error) {
  console.error('❌ Database service import failed:', error.message);
  process.exit(1);
}

// Test 4: App class import (this is likely where the hang occurs)
console.log('4. Testing App class import...');
try {
  const App = require('./dist/apps/api/src/app.js');
  console.log('✅ App class imported successfully');
  console.log('   - App constructor:', typeof App.default);
} catch (error) {
  console.error('❌ App class import failed:', error.message);
  process.exit(1);
}

// Test 5: Create App instance (this might hang)
console.log('5. Testing App instance creation...');
try {
  const App = require('./dist/apps/api/src/app.js');
  const app = new App.default();
  console.log('✅ App instance created successfully');
} catch (error) {
  console.error('❌ App instance creation failed:', error.message);
  process.exit(1);
}

console.log('🎉 All imports successful! The hang must be in app.initialize()');
