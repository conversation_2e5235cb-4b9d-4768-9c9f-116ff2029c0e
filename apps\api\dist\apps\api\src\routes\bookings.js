"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const bookingsController = __importStar(require("../controllers/bookings"));
const zod_1 = require("zod");
const router = (0, express_1.Router)();
// Validation schemas for bookings
const createBookingSchema = zod_1.z.object({
    serviceId: zod_1.z.string().min(1, 'Service ID is required'),
    message: zod_1.z.string().min(10, 'Message must be at least 10 characters').max(1000, 'Message must be less than 1000 characters'),
    requirements: zod_1.z.array(zod_1.z.string()).optional(),
    budget: zod_1.z.number().min(0, 'Budget must be positive').optional(),
    deadline: zod_1.z.string().datetime().optional(),
    location: zod_1.z.object({
        governorate: zod_1.z.string().optional(),
        city: zod_1.z.string().optional(),
        address: zod_1.z.string().optional(),
        coordinates: zod_1.z.object({
            lat: zod_1.z.number(),
            lng: zod_1.z.number()
        }).optional()
    }).optional(),
    contactPreference: zod_1.z.enum(['CHAT', 'PHONE', 'EMAIL']).default('CHAT'),
    urgency: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM')
});
const updateBookingStatusSchema = zod_1.z.object({
    status: zod_1.z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']),
    message: zod_1.z.string().optional(),
    estimatedDelivery: zod_1.z.string().datetime().optional(),
    finalPrice: zod_1.z.number().min(0).optional()
});
const searchBookingsSchema = zod_1.z.object({
    status: zod_1.z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
    serviceId: zod_1.z.string().optional(),
    expertId: zod_1.z.string().optional(),
    clientId: zod_1.z.string().optional(),
    urgency: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
    fromDate: zod_1.z.string().datetime().optional(),
    toDate: zod_1.z.string().datetime().optional(),
    sortBy: zod_1.z.enum(['created_at', 'deadline', 'budget', 'status']).default('created_at'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    page: zod_1.z.number().min(1).default(1),
    limit: zod_1.z.number().min(1).max(50).default(20)
});
/**
 * @swagger
 * components:
 *   schemas:
 *     Booking:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Booking ID
 *         serviceId:
 *           type: string
 *           description: Service ID
 *         clientId:
 *           type: string
 *           description: Client ID
 *         expertId:
 *           type: string
 *           description: Expert ID
 *         status:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *           description: Booking status
 *         message:
 *           type: string
 *           description: Initial message from client
 *         requirements:
 *           type: array
 *           items:
 *             type: string
 *           description: Project requirements
 *         budget:
 *           type: number
 *           description: Client's budget
 *         finalPrice:
 *           type: number
 *           description: Final agreed price
 *         deadline:
 *           type: string
 *           format: date-time
 *           description: Project deadline
 *         estimatedDelivery:
 *           type: string
 *           format: date-time
 *           description: Expert's estimated delivery date
 *         location:
 *           type: object
 *           properties:
 *             governorate:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *             coordinates:
 *               type: object
 *               properties:
 *                 lat:
 *                   type: number
 *                 lng:
 *                   type: number
 *         contactPreference:
 *           type: string
 *           enum: [CHAT, PHONE, EMAIL]
 *           description: Preferred contact method
 *         urgency:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *           description: Project urgency level
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     BookingResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           $ref: '#/components/schemas/Booking'
 *
 *     BookingsListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             bookings:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Booking'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: number
 *                 limit:
 *                   type: number
 *                 total:
 *                   type: number
 *                 totalPages:
 *                   type: number
 */
/**
 * @swagger
 * /api/v1/bookings:
 *   post:
 *     summary: Create a new booking request
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceId
 *               - message
 *             properties:
 *               serviceId:
 *                 type: string
 *                 example: "service_123"
 *               message:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 1000
 *                 example: "أحتاج إلى تطوير موقع إلكتروني لشركتي الناشئة"
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["تصميم متجاوب", "لوحة إدارة", "نظام دفع"]
 *               budget:
 *                 type: number
 *                 minimum: 0
 *                 example: 1000
 *               deadline:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-02-15T00:00:00Z"
 *               urgency:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *                 example: "MEDIUM"
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Client role required
 */
router.post('/', auth_1.authenticate, auth_1.clientOnly, (0, validation_1.validateBody)(createBookingSchema), bookingsController.createBooking);
/**
 * @swagger
 * /api/v1/bookings:
 *   get:
 *     summary: Get bookings with filters
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: serviceId
 *         schema:
 *           type: string
 *         description: Filter by service ID
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *         description: Filter by urgency level
 *       - in: query
 *         name: fromDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter bookings from this date
 *       - in: query
 *         name: toDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter bookings to this date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created_at, deadline, budget, status]
 *         description: Sort by field
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/', auth_1.authenticate, auth_1.anyUser, (0, validation_1.validateQuery)(searchBookingsSchema), bookingsController.getBookings);
/**
 * @swagger
 * /api/v1/bookings/{id}:
 *   get:
 *     summary: Get booking by ID
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     responses:
 *       200:
 *         description: Booking retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied
 *       404:
 *         description: Booking not found
 */
router.get('/:id', auth_1.authenticate, auth_1.anyUser, bookingsController.getBookingById);
/**
 * @swagger
 * /api/v1/bookings/{id}/status:
 *   put:
 *     summary: Update booking status
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *                 example: "ACCEPTED"
 *               message:
 *                 type: string
 *                 example: "أوافق على المشروع وسأبدأ العمل فوراً"
 *               estimatedDelivery:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-02-10T00:00:00Z"
 *               finalPrice:
 *                 type: number
 *                 minimum: 0
 *                 example: 800
 *     responses:
 *       200:
 *         description: Booking status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingResponse'
 *       400:
 *         description: Validation error or invalid status transition
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied
 *       404:
 *         description: Booking not found
 */
router.put('/:id/status', auth_1.authenticate, auth_1.anyUser, (0, validation_1.validateBody)(updateBookingStatusSchema), bookingsController.updateBookingStatus);
/**
 * @swagger
 * /api/v1/bookings/my/client:
 *   get:
 *     summary: Get current user's bookings as client
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Client bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/my/client', auth_1.authenticate, (0, validation_1.validateQuery)(searchBookingsSchema), bookingsController.getMyClientBookings);
/**
 * @swagger
 * /api/v1/bookings/my/expert:
 *   get:
 *     summary: Get current user's bookings as expert
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED]
 *         description: Filter by booking status
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 50
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Expert bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookingsListResponse'
 *       401:
 *         description: Authentication required
 */
router.get('/my/expert', auth_1.authenticate, (0, validation_1.validateQuery)(searchBookingsSchema), bookingsController.getMyExpertBookings);
exports.default = router;
//# sourceMappingURL=bookings.js.map