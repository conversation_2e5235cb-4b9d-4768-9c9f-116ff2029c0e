"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSearchSuggestions = exports.globalSearch = exports.searchExperts = exports.searchServices = void 0;
const supabase_1 = require("@freela/database/src/supabase");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
// Utility function to calculate distance between two coordinates
const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};
// Map service data for search results
const mapServiceSearchResult = (service, userLat, userLng) => {
    const result = {
        type: 'service',
        id: service.id,
        title: service.title,
        description: service.description,
        category: service.category,
        subcategory: service.subcategory,
        price: service.base_price,
        priceType: service.price_type,
        serviceType: service.service_type,
        rating: service.expert_profiles?.rating || 0,
        location: service.location,
        tags: service.tags,
        expert: {
            id: service.expert_profiles?.id,
            name: `${service.expert_profiles?.users?.first_name} ${service.expert_profiles?.users?.last_name}`,
            rating: service.expert_profiles?.rating || 0,
            totalReviews: service.expert_profiles?.total_reviews || 0,
            isVerified: service.expert_profiles?.is_verified || false
        },
        createdAt: service.created_at
    };
    // Calculate distance if user location is provided
    if (userLat && userLng && service.location?.coordinates) {
        result.distance = calculateDistance(userLat, userLng, service.location.coordinates.lat, service.location.coordinates.lng);
    }
    return result;
};
// Map expert data for search results
const mapExpertSearchResult = (expert, userLat, userLng) => {
    const result = {
        type: 'expert',
        id: expert.id,
        title: `${expert.users?.first_name} ${expert.users?.last_name}`,
        description: expert.bio,
        category: expert.skills?.[0] || 'General',
        price: expert.hourly_rate,
        rating: expert.rating || 0,
        location: expert.location,
        skills: expert.skills,
        experience: expert.experience,
        availability: expert.availability,
        totalReviews: expert.total_reviews || 0,
        completedProjects: expert.completed_projects || 0,
        isVerified: expert.is_verified || false,
        isAvailable: expert.is_available || false,
        languages: expert.languages,
        createdAt: expert.created_at
    };
    // Calculate distance if user location is provided
    if (userLat && userLng && expert.location?.coordinates) {
        result.distance = calculateDistance(userLat, userLng, expert.location.coordinates.lat, expert.location.coordinates.lng);
    }
    return result;
};
/**
 * Search services with advanced filters and location-based sorting
 */
const searchServices = async (req, res) => {
    try {
        const { q, category, subcategory, serviceType, priceType, minPrice, maxPrice, governorate, city, tags, skills, minRating, maxDistance, lat, lng, sortBy = 'rating', sortOrder = 'desc', page = 1, limit = 20, isActive = true } = req.query;
        let query = supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          rating,
          review_count,
          is_verified,
          users!inner(
            id,
            first_name,
            last_name
          )
        )
      `, { count: 'exact' });
        // Apply filters
        if (isActive !== undefined) {
            const isActiveBoolean = String(isActive) === 'true';
            if (isActiveBoolean) {
                query = query.eq('status', 'ACTIVE');
            }
            else {
                query = query.neq('status', 'ACTIVE');
            }
        }
        if (category) {
            query = query.eq('category', category);
        }
        if (subcategory) {
            query = query.eq('subcategory', subcategory);
        }
        if (serviceType) {
            query = query.eq('service_type', serviceType);
        }
        if (priceType) {
            query = query.eq('price_type', priceType);
        }
        if (minPrice) {
            query = query.gte('base_price', minPrice);
        }
        if (maxPrice) {
            query = query.lte('base_price', maxPrice);
        }
        if (minRating) {
            query = query.gte('expert_profiles.rating', minRating);
        }
        // Location filters
        if (governorate) {
            query = query.contains('location', { governorate });
        }
        if (city) {
            query = query.contains('location', { city });
        }
        // Text search
        if (q) {
            query = query.or(`title.ilike.%${q}%,description.ilike.%${q}%,category.ilike.%${q}%`);
        }
        // Tags search
        if (tags) {
            const tagArray = tags.toString().split(',').map(tag => tag.trim());
            query = query.overlaps('tags', tagArray);
        }
        // Skills search (search in expert skills)
        if (skills) {
            const skillArray = skills.toString().split(',').map(skill => skill.trim());
            query = query.overlaps('expert_profiles.skills', skillArray);
        }
        // Sorting (distance sorting will be handled after fetching)
        if (sortBy !== 'distance') {
            const sortField = sortBy === 'price' ? 'base_price' :
                sortBy === 'rating' ? 'expert_profiles.rating' :
                    sortBy === 'popularity' ? 'expert_profiles.total_reviews' :
                        'created_at';
            query = query.order(sortField, { ascending: sortOrder === 'asc' });
        }
        // Pagination
        const offset = (Number(page) - 1) * Number(limit);
        query = query.range(offset, offset + Number(limit) - 1);
        const { data: services, error, count } = await query;
        if (error) {
            logger_1.logger.error('Error searching services:', error);
            throw new errors_1.AppError('Failed to search services', 500);
        }
        let results = services?.map(service => mapServiceSearchResult(service, Number(lat), Number(lng))) || [];
        // Apply distance filter and sorting if location is provided
        if (lat && lng) {
            if (maxDistance) {
                results = results.filter(result => !result.distance || result.distance <= Number(maxDistance));
            }
            if (sortBy === 'distance') {
                results.sort((a, b) => {
                    const distA = a.distance || Infinity;
                    const distB = b.distance || Infinity;
                    return sortOrder === 'asc' ? distA - distB : distB - distA;
                });
            }
        }
        const totalPages = Math.ceil((count || 0) / Number(limit));
        res.json({
            success: true,
            message: 'Services search completed successfully',
            data: {
                results,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total: count || 0,
                    totalPages
                },
                filters: {
                    query: q,
                    category,
                    serviceType,
                    location: { governorate, city },
                    priceRange: { min: minPrice, max: maxPrice },
                    hasLocation: !!(lat && lng)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Search services error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.searchServices = searchServices;
/**
 * Search experts with advanced filters and location-based sorting
 */
const searchExperts = async (req, res) => {
    try {
        const { q, skills, category, governorate, city, availability, minRating, maxHourlyRate, minExperience, languages, maxDistance, lat, lng, isAvailable = true, isVerified, sortBy = 'rating', sortOrder = 'desc', page = 1, limit = 20 } = req.query;
        let query = supabase_1.supabaseAdmin
            .from('expert_profiles')
            .select(`
        *,
        users!inner(
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' });
        // Apply filters
        if (isAvailable !== undefined) {
            const isAvailableBoolean = String(isAvailable) === 'true';
            if (isAvailableBoolean) {
                query = query.eq('availability->status', 'AVAILABLE');
            }
            else {
                query = query.neq('availability->status', 'AVAILABLE');
            }
        }
        if (isVerified !== undefined) {
            query = query.eq('is_verified', isVerified);
        }
        if (availability) {
            query = query.eq('availability', availability);
        }
        if (minRating) {
            query = query.gte('rating', minRating);
        }
        if (maxHourlyRate) {
            query = query.lte('hourly_rate', maxHourlyRate);
        }
        if (minExperience) {
            query = query.gte('experience', minExperience);
        }
        // Location filters
        if (governorate) {
            query = query.contains('location', { governorate });
        }
        if (city) {
            query = query.contains('location', { city });
        }
        // Skills search
        if (skills) {
            const skillArray = skills.toString().split(',').map(skill => skill.trim());
            query = query.overlaps('skills', skillArray);
        }
        // Languages search
        if (languages) {
            const langArray = languages.toString().split(',').map(lang => lang.trim());
            query = query.overlaps('languages', langArray);
        }
        // Text search
        if (q) {
            query = query.or(`bio.ilike.%${q}%,skills.cs.{${q}}`);
        }
        // Sorting (distance sorting will be handled after fetching)
        if (sortBy !== 'distance') {
            const sortField = sortBy === 'hourly_rate' ? 'hourly_rate' :
                sortBy === 'total_reviews' ? 'total_reviews' :
                    sortBy === 'experience' ? 'experience' :
                        'rating';
            query = query.order(sortField, { ascending: sortOrder === 'asc' });
        }
        // Pagination
        const offset = (Number(page) - 1) * Number(limit);
        query = query.range(offset, offset + Number(limit) - 1);
        const { data: experts, error, count } = await query;
        if (error) {
            logger_1.logger.error('Error searching experts:', error);
            throw new errors_1.AppError('Failed to search experts', 500);
        }
        let results = experts?.map(expert => mapExpertSearchResult(expert, Number(lat), Number(lng))) || [];
        // Apply distance filter and sorting if location is provided
        if (lat && lng) {
            if (maxDistance) {
                results = results.filter(result => !result.distance || result.distance <= Number(maxDistance));
            }
            if (sortBy === 'distance') {
                results.sort((a, b) => {
                    const distA = a.distance || Infinity;
                    const distB = b.distance || Infinity;
                    return sortOrder === 'asc' ? distA - distB : distB - distA;
                });
            }
        }
        const totalPages = Math.ceil((count || 0) / Number(limit));
        res.json({
            success: true,
            message: 'Experts search completed successfully',
            data: {
                results,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total: count || 0,
                    totalPages
                },
                filters: {
                    query: q,
                    skills: skills?.toString().split(','),
                    location: { governorate, city },
                    availability,
                    hasLocation: !!(lat && lng)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Search experts error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.searchExperts = searchExperts;
/**
 * Global search across services and experts
 */
const globalSearch = async (req, res) => {
    try {
        const { q, type = 'all', governorate, city, lat, lng, maxDistance, page = 1, limit = 20 } = req.query;
        const results = {
            services: [],
            experts: []
        };
        const searchLimit = type === 'all' ? Math.ceil(Number(limit) / 2) : Number(limit);
        // Search services
        if (type === 'all' || type === 'services') {
            let servicesQuery = supabase_1.supabaseAdmin
                .from('services')
                .select(`
          *,
          expert_profiles!inner(
            id,
            rating,
            total_reviews,
            is_verified,
            users!inner(
              id,
              first_name,
              last_name
            )
          )
        `)
                .eq('is_active', true)
                .or(`title.ilike.%${q}%,description.ilike.%${q}%,category.ilike.%${q}%`);
            if (governorate) {
                servicesQuery = servicesQuery.contains('location', { governorate });
            }
            if (city) {
                servicesQuery = servicesQuery.contains('location', { city });
            }
            servicesQuery = servicesQuery
                .order('expert_profiles.rating', { ascending: false })
                .limit(searchLimit);
            const { data: services, error: servicesError } = await servicesQuery;
            if (!servicesError && services) {
                results.services = services.map(service => mapServiceSearchResult(service, Number(lat), Number(lng)));
            }
        }
        // Search experts
        if (type === 'all' || type === 'experts') {
            let expertsQuery = supabase_1.supabaseAdmin
                .from('expert_profiles')
                .select(`
          *,
          users!inner(
            id,
            first_name,
            last_name
          )
        `)
                .eq('is_available', true)
                .or(`bio.ilike.%${q}%,skills.cs.{${q}}`);
            if (governorate) {
                expertsQuery = expertsQuery.contains('location', { governorate });
            }
            if (city) {
                expertsQuery = expertsQuery.contains('location', { city });
            }
            expertsQuery = expertsQuery
                .order('rating', { ascending: false })
                .limit(searchLimit);
            const { data: experts, error: expertsError } = await expertsQuery;
            if (!expertsError && experts) {
                results.experts = experts.map(expert => mapExpertSearchResult(expert, Number(lat), Number(lng)));
            }
        }
        // Apply distance filtering if location is provided
        if (lat && lng && maxDistance) {
            results.services = results.services.filter((service) => !service.distance || service.distance <= Number(maxDistance));
            results.experts = results.experts.filter((expert) => !expert.distance || expert.distance <= Number(maxDistance));
        }
        res.json({
            success: true,
            message: 'Global search completed successfully',
            data: {
                results,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    totalServices: results.services.length,
                    totalExperts: results.experts.length
                },
                query: q,
                filters: {
                    type,
                    location: { governorate, city },
                    hasLocation: !!(lat && lng)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Global search error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.globalSearch = globalSearch;
/**
 * Get search suggestions and autocomplete
 */
const getSearchSuggestions = async (req, res) => {
    try {
        const { q, type, limit = 10 } = req.query;
        const suggestions = [];
        // Get service titles and categories
        if (!type || type === 'services') {
            const { data: services } = await supabase_1.supabaseAdmin
                .from('services')
                .select('title, category')
                .eq('is_active', true)
                .or(`title.ilike.%${q}%,category.ilike.%${q}%`)
                .limit(Number(limit));
            if (services) {
                services.forEach(service => {
                    if (service.title.toLowerCase().includes(q?.toString().toLowerCase() || '')) {
                        suggestions.push({
                            text: service.title,
                            type: 'service',
                            count: 1
                        });
                    }
                    if (service.category.toLowerCase().includes(q?.toString().toLowerCase() || '')) {
                        suggestions.push({
                            text: service.category,
                            type: 'category',
                            count: 1
                        });
                    }
                });
            }
        }
        // Get expert skills and names
        if (!type || type === 'experts') {
            const { data: experts } = await supabase_1.supabaseAdmin
                .from('expert_profiles')
                .select(`
          skills,
          users!inner(
            first_name,
            last_name
          )
        `)
                .eq('is_available', true)
                .limit(Number(limit));
            if (experts) {
                experts.forEach(expert => {
                    const fullName = `${expert.users[0]?.first_name} ${expert.users[0]?.last_name}`;
                    if (fullName.toLowerCase().includes(q?.toString().toLowerCase() || '')) {
                        suggestions.push({
                            text: fullName,
                            type: 'expert',
                            count: 1
                        });
                    }
                    if (expert.skills) {
                        expert.skills.forEach((skill) => {
                            if (skill.toLowerCase().includes(q?.toString().toLowerCase() || '')) {
                                suggestions.push({
                                    text: skill,
                                    type: 'skill',
                                    count: 1
                                });
                            }
                        });
                    }
                });
            }
        }
        // Remove duplicates and limit results
        const uniqueSuggestions = suggestions
            .filter((suggestion, index, self) => index === self.findIndex(s => s.text === suggestion.text && s.type === suggestion.type))
            .slice(0, Number(limit));
        res.json({
            success: true,
            message: 'Search suggestions retrieved successfully',
            data: {
                suggestions: uniqueSuggestions,
                query: q
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get search suggestions error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.getSearchSuggestions = getSearchSuggestions;
//# sourceMappingURL=search.js.map