import { PrismaClient, Prisma } from '@prisma/client';

// Define PrismaClient type using inline import for CommonJS compatibility
type PrismaClientType = import('@prisma/client').PrismaClient;

let prisma: PrismaClientType | undefined;

// This is a singleton pattern to ensure we only have one instance of Prisma Client.
const getPrismaInstance = (): PrismaClientType => {
  if (prisma) {
    return prisma;
  }

  const newPrismaInstance = new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });

  // In development, use a global variable to preserve the client across hot reloads.
  if (process.env.NODE_ENV !== 'production') {
    if (!(globalThis as any).__prisma) {
      (globalThis as any).__prisma = newPrismaInstance;
    }
    prisma = (globalThis as any).__prisma;
  } else {
    prisma = newPrismaInstance;
  }

  return prisma!; // Non-null assertion since we just created it
};

// Immediately get the instance to be used by the app
const prismaInstance = getPrismaInstance();

// Graceful shutdown logic
const setupGracefulShutdown = (client: PrismaClientType): void => {
  let isShuttingDown = false;
  const shutdown = async (signal: string): Promise<void> => {
    if (isShuttingDown) return;
    isShuttingDown = true;
    console.log(`Received ${signal}. Disconnecting database...`);
    await client.$disconnect();
    console.log('Database disconnected.');
    process.exit(0);
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
};

setupGracefulShutdown(prismaInstance);

// Database connection utilities with timeout and graceful failure
const connectDatabase = async (): Promise<void> => {
  const client = getPrismaInstance();
  try {
    // Race connection against a timeout
    await Promise.race([
      client.$connect(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timed out after 10 seconds')), 10000)
      ),
    ]);
    console.log('✅ Database connected successfully');
  } catch (error) {
    if (error instanceof Error) {
      console.error('❌ Database connection failed:', error.message);
    } else {
      console.error('❌ An unexpected error occurred during database connection:', error);
    }
    console.warn('⚠️ Server is starting without a database connection. Some features will be unavailable.');
    // Do not re-throw; allow the application to start in a degraded state.
    return; // Explicitly return to avoid any potential re-throw
  }
};

const disconnectDatabase = async (): Promise<void> => {
  const client = getPrismaInstance();
  try {
    await client.$disconnect();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    if (error instanceof Error) {
      console.error('❌ Database disconnection failed:', error.message);
    } else {
      console.error('❌ An unexpected error occurred during database disconnection:', error);
    }
    // In a disconnect scenario, we should probably throw to indicate a problem.
    throw error;
  }
};

// Health check for the database connection
const checkDatabaseHealth = async (): Promise<boolean> => {
  const client = getPrismaInstance();
  try {
    await client.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    // Don't spam logs on health checks
    return false;
  }
};

/**
 * Wrapper for Prisma transactions.
 * @template T
 * @param {(tx: any) => Promise<T>} callback
 * @returns {Promise<T>}
 */
const withTransaction = async <T>(
  callback: (tx: any) => Promise<T>
): Promise<T> => {
  const client = getPrismaInstance();
  return await client.$transaction(callback);
};

// Export using ES modules
export {
  prismaInstance as prisma,
  // connectDatabase,        // COMMENTED OUT - conflicts with Supabase version
  // disconnectDatabase,     // COMMENTED OUT - conflicts with Supabase version
  // checkDatabaseHealth,    // COMMENTED OUT - conflicts with Supabase version
  withTransaction,
  getPrismaInstance,
  Prisma,
  PrismaClient,
};

export default prismaInstance;
